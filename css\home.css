/* UVAMAYU - Home Page Styles */

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-slider {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-slide.active {
    opacity: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(132, 20, 42, 0.6) 100%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--color-white);
    max-width: 800px;
    padding: 2rem;
}

.hero-title {
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
    font-size: clamp(1.2rem, 2.5vw, 1.5rem);
    margin-bottom: 2.5rem;
    line-height: 1.6;
    opacity: 0.95;
    font-weight: 300;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-actions .btn {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    min-width: 200px;
}

/* Hero Navigation */
.hero-nav {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 2rem;
    z-index: 3;
}

.hero-nav-btn {
    width: 50px;
    height: 50px;
    border: 2px solid var(--color-white);
    background: rgba(255, 255, 255, 0.1);
    color: var(--color-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-medium);
    backdrop-filter: blur(10px);
}

.hero-nav-btn:hover {
    background: var(--color-white);
    color: var(--color-primary);
    transform: scale(1.1);
}

.hero-dots {
    display: flex;
    gap: 1rem;
}

.hero-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all var(--transition-medium);
}

.hero-dot.active,
.hero-dot:hover {
    background: var(--color-secondary);
    transform: scale(1.2);
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    z-index: 3;
}

.scroll-line {
    width: 2px;
    height: 60px;
    background: linear-gradient(to bottom, transparent, var(--color-secondary), transparent);
    animation: scrollPulse 2s infinite;
}

.scroll-text {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    color: var(--color-white);
    font-size: 0.9rem;
    letter-spacing: 0.1em;
    opacity: 0.8;
}

@keyframes scrollPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Brand Story Section */
.brand-story {
    padding: 6rem 0;
}

.story-content h2 {
    margin-bottom: 2rem;
}

.brand-attributes {
    margin: 2rem 0;
    display: grid;
    gap: 1rem;
}

.attribute-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(196, 154, 82, 0.1);
    border-radius: 0.5rem;
    transition: all var(--transition-medium);
}

.attribute-item:hover {
    background: rgba(196, 154, 82, 0.2);
    transform: translateX(10px);
}

.attribute-item i {
    color: var(--color-secondary);
    font-size: 1.2rem;
}

.story-image {
    position: relative;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
}

.story-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.story-image:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--color-white);
    padding: 2rem;
    transform: translateY(100%);
    transition: transform var(--transition-medium);
}

.story-image:hover .image-overlay {
    transform: translateY(0);
}

.overlay-content h3 {
    color: var(--color-secondary);
    margin-bottom: 0.5rem;
}

/* Featured Products Section */
.featured-products {
    padding: 6rem 0;
}

.section-header {
    margin-bottom: 4rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto;
}

.products-grid {
    margin-bottom: 4rem;
}

.product-card {
    background: var(--color-white);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.product-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-medium);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-actions {
    display: flex;
    gap: 1rem;
    flex-direction: column;
}

.product-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-secondary);
    color: var(--color-white);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 2;
}

.product-info {
    padding: 1.5rem;
}

.product-category {
    color: var(--color-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.product-title {
    color: var(--color-primary);
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.product-type {
    color: var(--color-medium-gray);
    font-style: italic;
    margin-bottom: 1rem;
}

.product-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--color-medium-gray);
}

.product-detail i {
    color: var(--color-secondary);
    width: 16px;
}

.product-pricing {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--color-light-gray);
}

.price-main {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-primary);
}

.price-bulk {
    font-size: 0.9rem;
    color: var(--color-medium-gray);
    margin-top: 0.25rem;
}

.btn-sm {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
}

.no-products {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    color: rgba(255, 255, 255, 0.7);
}

.no-products i {
    font-size: 4rem;
    color: var(--color-secondary);
    margin-bottom: 1rem;
}

/* Process Section */
.process-section {
    padding: 6rem 0;
}

.process-steps {
    margin: 4rem 0;
}

.process-step {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 4rem;
    padding: 2rem 0;
}

.process-step:nth-child(even) {
    direction: rtl;
}

.process-step:nth-child(even) > * {
    direction: ltr;
}

.step-image {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

.step-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.process-step:hover .step-image img {
    transform: scale(1.05);
}

.step-content {
    position: relative;
}

.step-number {
    font-family: var(--font-primary);
    font-size: 4rem;
    font-weight: 800;
    color: var(--color-secondary);
    opacity: 0.3;
    position: absolute;
    top: -2rem;
    left: -1rem;
    z-index: -1;
}

.step-content h3 {
    margin-bottom: 1rem;
    color: var(--color-primary);
}

.step-content p {
    font-size: 1.1rem;
    line-height: 1.7;
}

/* Experience Section */
.experience-section {
    padding: 6rem 0;
}

.experience-features {
    margin: 2rem 0;
}

.feature-item {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--color-white);
    border-radius: 1rem;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-medium);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-content h4 {
    color: var(--color-primary);
    margin-bottom: 0.5rem;
}

.experience-gallery {
    display: grid;
    gap: 1rem;
    grid-template-rows: 2fr 1fr;
}

.gallery-main {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

.gallery-secondary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.gallery-secondary img {
    border-radius: 1rem;
    box-shadow: var(--shadow-light);
    transition: transform var(--transition-medium);
}

.gallery-secondary img:hover {
    transform: scale(1.05);
}

/* Testimonials Section */
.testimonials-section {
    padding: 6rem 0;
}

.testimonials-grid {
    margin-bottom: 4rem;
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(196, 154, 82, 0.3);
    border-radius: 1rem;
    padding: 2rem;
    backdrop-filter: blur(10px);
    transition: all var(--transition-medium);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.stars {
    color: var(--color-secondary);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.testimonial-content p {
    color: rgba(255, 255, 255, 0.9);
    font-style: italic;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-info h4 {
    color: var(--color-white);
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.author-info span {
    color: var(--color-secondary);
    font-size: 0.9rem;
}

/* Contact CTA Section */
.contact-cta {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--color-light-gray) 0%, var(--color-white) 100%);
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    margin-bottom: 1.5rem;
}

.cta-content .text-large {
    margin-bottom: 3rem;
    color: var(--color-medium-gray);
}

.cta-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-lg {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
    min-width: 220px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .process-step {
        gap: 2rem;
    }
    
    .step-image img {
        height: 250px;
    }
    
    .hero-actions {
        gap: 1rem;
    }
    
    .hero-actions .btn {
        min-width: 180px;
        padding: 1rem 2rem;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 80vh;
    }
    
    .hero-content {
        padding: 1rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-actions .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .scroll-indicator {
        display: none;
    }
    
    .process-step {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .process-step:nth-child(even) {
        direction: ltr;
    }
    
    .step-number {
        position: static;
        display: block;
        margin-bottom: 1rem;
        opacity: 1;
    }
    
    .experience-gallery {
        grid-template-rows: auto;
        margin-top: 2rem;
    }
    
    .gallery-secondary {
        grid-template-columns: 1fr;
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
    }
    
    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-lg {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 480px) {
    .section {
        padding: 3rem 0;
    }
    
    .hero-nav {
        bottom: 1rem;
        gap: 1rem;
    }
    
    .hero-nav-btn {
        width: 40px;
        height: 40px;
    }
    
    .brand-attributes {
        gap: 0.5rem;
    }
    
    .attribute-item {
        padding: 0.75rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .testimonial-card {
        padding: 1.5rem;
    }
}
