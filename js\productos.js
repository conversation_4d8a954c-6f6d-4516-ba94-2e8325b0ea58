// UVAMAYU - Products Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initProductFilters();
    initProductSort();
    initViewToggle();
    initDropdowns();
    initProductSearch();
});

// Product Filters
function initProductFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Build URL with filter
            const url = new URL(window.location);
            url.search = ''; // Clear existing params
            
            switch(filter) {
                case 'pisco':
                    url.searchParams.set('categoria', 'Pisco');
                    break;
                case 'vino':
                    url.searchParams.set('categoria', 'Vino');
                    break;
                case 'destacados':
                    url.searchParams.set('destacado', '1');
                    break;
                case 'all':
                default:
                    // No parameters for 'all'
                    break;
            }
            
            // Navigate to filtered URL
            window.location.href = url.toString();
        });
    });
}

// Product Sorting
function initProductSort() {
    const sortSelect = document.getElementById('sortProducts');
    const productsGrid = document.getElementById('productsGrid');
    
    if (!sortSelect || !productsGrid) return;
    
    sortSelect.addEventListener('change', function() {
        const sortValue = this.value;
        const productCards = Array.from(productsGrid.querySelectorAll('.product-card'));
        
        // Sort products based on selected criteria
        productCards.sort((a, b) => {
            switch(sortValue) {
                case 'name':
                    const nameA = a.getAttribute('data-name').toLowerCase();
                    const nameB = b.getAttribute('data-name').toLowerCase();
                    return nameA.localeCompare(nameB);
                    
                case 'price-low':
                    const priceA = parseFloat(a.getAttribute('data-price'));
                    const priceB = parseFloat(b.getAttribute('data-price'));
                    return priceA - priceB;
                    
                case 'price-high':
                    const priceA2 = parseFloat(a.getAttribute('data-price'));
                    const priceB2 = parseFloat(b.getAttribute('data-price'));
                    return priceB2 - priceA2;
                    
                case 'featured':
                    const featuredA = parseInt(a.getAttribute('data-featured'));
                    const featuredB = parseInt(b.getAttribute('data-featured'));
                    if (featuredA !== featuredB) {
                        return featuredB - featuredA; // Featured first
                    }
                    // If same featured status, sort by name
                    const nameA2 = a.getAttribute('data-name').toLowerCase();
                    const nameB2 = b.getAttribute('data-name').toLowerCase();
                    return nameA2.localeCompare(nameB2);
                    
                default:
                    return 0;
            }
        });
        
        // Re-append sorted elements
        productCards.forEach(card => {
            productsGrid.appendChild(card);
        });
        
        // Add animation to sorted items
        productCards.forEach((card, index) => {
            card.style.animation = 'none';
            setTimeout(() => {
                card.style.animation = `fadeInUp 0.6s ease forwards`;
                card.style.animationDelay = `${index * 0.1}s`;
            }, 10);
        });
    });
}

// View Toggle (Grid/List)
function initViewToggle() {
    const viewButtons = document.querySelectorAll('.view-btn');
    const productsGrid = document.getElementById('productsGrid');
    
    if (!productsGrid) return;
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.getAttribute('data-view');
            
            // Update active button
            viewButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Update grid class
            if (view === 'list') {
                productsGrid.classList.add('list-view');
                productsGrid.classList.remove('grid-3');
            } else {
                productsGrid.classList.remove('list-view');
                productsGrid.classList.add('grid-3');
            }
            
            // Store preference in localStorage
            localStorage.setItem('productsView', view);
        });
    });
    
    // Load saved view preference
    const savedView = localStorage.getItem('productsView');
    if (savedView) {
        const viewButton = document.querySelector(`[data-view="${savedView}"]`);
        if (viewButton) {
            viewButton.click();
        }
    }
}

// Dropdown Functionality
function initDropdowns() {
    const dropdowns = document.querySelectorAll('.filter-dropdown');
    
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (!toggle || !menu) return;
        
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Close other dropdowns
            dropdowns.forEach(otherDropdown => {
                if (otherDropdown !== dropdown) {
                    otherDropdown.classList.remove('active');
                }
            });
            
            // Toggle current dropdown
            dropdown.classList.toggle('active');
        });
        
        // Close dropdown when clicking on items
        const items = menu.querySelectorAll('.dropdown-item');
        items.forEach(item => {
            item.addEventListener('click', function() {
                dropdown.classList.remove('active');
            });
        });
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.filter-dropdown')) {
            dropdowns.forEach(dropdown => {
                dropdown.classList.remove('active');
            });
        }
    });
}

// Product Search Enhancement
function initProductSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchForm = document.querySelector('.search-form');
    
    if (!searchInput || !searchForm) return;
    
    // Add search suggestions (if you want to implement this feature)
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                // You can implement search suggestions here
                // For now, we'll just add a visual indicator
                this.classList.add('searching');
                
                setTimeout(() => {
                    this.classList.remove('searching');
                }, 500);
            }, 300);
        }
    });
    
    // Handle form submission
    searchForm.addEventListener('submit', function(e) {
        const query = searchInput.value.trim();
        
        if (!query) {
            e.preventDefault();
            searchInput.focus();
            showNotification('Por favor ingresa un término de búsqueda', 'warning');
        }
    });
    
    // Clear search functionality
    const clearSearch = document.createElement('button');
    clearSearch.type = 'button';
    clearSearch.className = 'clear-search';
    clearSearch.innerHTML = '<i class="fas fa-times"></i>';
    clearSearch.title = 'Limpiar búsqueda';
    
    if (searchInput.value) {
        searchInput.parentNode.appendChild(clearSearch);
        
        clearSearch.addEventListener('click', function() {
            searchInput.value = '';
            searchForm.submit();
        });
    }
}

// Product Card Interactions
function initProductCardInteractions() {
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        const overlay = card.querySelector('.product-overlay');
        const actions = card.querySelectorAll('.product-actions .btn');
        
        if (!overlay || !actions.length) return;
        
        // Initialize button positions for animation
        actions.forEach((btn, index) => {
            btn.style.transform = 'translateY(20px)';
            btn.style.opacity = '0';
            btn.style.transition = `all 0.3s ease ${index * 0.1}s`;
        });
        
        card.addEventListener('mouseenter', function() {
            actions.forEach(btn => {
                btn.style.transform = 'translateY(0)';
                btn.style.opacity = '1';
            });
        });
        
        card.addEventListener('mouseleave', function() {
            actions.forEach(btn => {
                btn.style.transform = 'translateY(20px)';
                btn.style.opacity = '0';
            });
        });
        
        // Add click tracking for analytics (optional)
        card.addEventListener('click', function(e) {
            // Only track if not clicking on action buttons
            if (!e.target.closest('.product-actions')) {
                const productName = this.getAttribute('data-name');
                const productCategory = this.getAttribute('data-category');
                
                // You can send analytics data here
                console.log('Product viewed:', productName, productCategory);
            }
        });
    });
}

// Initialize product card interactions
initProductCardInteractions();

// Lazy loading for product images
function initLazyLoading() {
    const images = document.querySelectorAll('.product-image[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    
                    // Add loading class
                    img.classList.add('loading');
                    
                    // Create a new image to preload
                    const newImg = new Image();
                    newImg.onload = function() {
                        img.src = this.src;
                        img.classList.remove('loading');
                        img.classList.add('loaded');
                    };
                    newImg.src = img.src;
                    
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Initialize lazy loading
initLazyLoading();

// Filter tags removal
function initFilterTags() {
    const filterTags = document.querySelectorAll('.filter-tag a');
    
    filterTags.forEach(tag => {
        tag.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            // Navigate to the href after a short delay for visual feedback
            setTimeout(() => {
                window.location.href = this.href;
            }, 200);
        });
    });
}

// Initialize filter tags
initFilterTags();

// Add CSS for search and loading states
const style = document.createElement('style');
style.textContent = `
    .search-input.searching {
        background-image: linear-gradient(90deg, transparent, rgba(196, 154, 82, 0.1), transparent);
        background-size: 200% 100%;
        animation: searchShimmer 1s infinite;
    }
    
    @keyframes searchShimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }
    
    .clear-search {
        position: absolute;
        right: 60px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--color-medium-gray);
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: all var(--transition-fast);
    }
    
    .clear-search:hover {
        background: var(--color-light-gray);
        color: var(--color-primary);
    }
    
    .product-image.loading {
        opacity: 0.7;
        filter: blur(2px);
    }
    
    .product-image.loaded {
        opacity: 1;
        filter: none;
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);
