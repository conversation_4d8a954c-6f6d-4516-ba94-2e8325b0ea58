// UVAMAYU - Home Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initHeroSlider();
    initCounters();
    initParallaxEffects();
    initProductHovers();
});

// Hero Slider Functionality
function initHeroSlider() {
    const slides = document.querySelectorAll('.hero-slide');
    const dots = document.querySelectorAll('.hero-dot');
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');
    
    let currentSlide = 0;
    let slideInterval;
    
    if (slides.length === 0) return;
    
    // Auto-play functionality
    function startSlideshow() {
        slideInterval = setInterval(() => {
            nextSlide();
        }, 5000); // Change slide every 5 seconds
    }
    
    function stopSlideshow() {
        clearInterval(slideInterval);
    }
    
    // Show specific slide
    function showSlide(index) {
        // Remove active class from all slides and dots
        slides.forEach(slide => slide.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));
        
        // Add active class to current slide and dot
        slides[index].classList.add('active');
        dots[index].classList.add('active');
        
        currentSlide = index;
    }
    
    // Next slide
    function nextSlide() {
        const nextIndex = (currentSlide + 1) % slides.length;
        showSlide(nextIndex);
    }
    
    // Previous slide
    function prevSlide() {
        const prevIndex = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(prevIndex);
    }
    
    // Event listeners
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            stopSlideshow();
            nextSlide();
            startSlideshow();
        });
    }
    
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            stopSlideshow();
            prevSlide();
            startSlideshow();
        });
    }
    
    // Dot navigation
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            stopSlideshow();
            showSlide(index);
            startSlideshow();
        });
    });
    
    // Pause on hover
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        heroSection.addEventListener('mouseenter', stopSlideshow);
        heroSection.addEventListener('mouseleave', startSlideshow);
    }
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'ArrowLeft') {
            stopSlideshow();
            prevSlide();
            startSlideshow();
        } else if (e.key === 'ArrowRight') {
            stopSlideshow();
            nextSlide();
            startSlideshow();
        }
    });
    
    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    
    if (heroSection) {
        heroSection.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        });
        
        heroSection.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });
    }
    
    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;
        
        if (Math.abs(diff) > swipeThreshold) {
            stopSlideshow();
            if (diff > 0) {
                nextSlide(); // Swipe left - next slide
            } else {
                prevSlide(); // Swipe right - previous slide
            }
            startSlideshow();
        }
    }
    
    // Start the slideshow
    startSlideshow();
}

// Animated Counters
function initCounters() {
    const counters = document.querySelectorAll('.counter');
    
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    counters.forEach(counter => {
        observer.observe(counter);
    });
    
    function animateCounter(element) {
        const target = parseInt(element.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    }
}

// Enhanced Parallax Effects
function initParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.parallax-element');
    
    if (parallaxElements.length === 0) return;
    
    function updateParallax() {
        const scrolled = window.pageYOffset;
        const windowHeight = window.innerHeight;
        
        parallaxElements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const elementTop = rect.top + scrolled;
            const elementHeight = rect.height;
            
            // Check if element is in viewport
            if (rect.bottom >= 0 && rect.top <= windowHeight) {
                const speed = element.getAttribute('data-speed') || 0.5;
                const yPos = -(scrolled - elementTop) * speed;
                
                element.style.transform = `translateY(${yPos}px)`;
            }
        });
    }
    
    // Throttled scroll event
    let ticking = false;
    
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }
    
    function handleScroll() {
        ticking = false;
        requestTick();
    }
    
    window.addEventListener('scroll', handleScroll);
    
    // Initial call
    updateParallax();
}

// Product Card Hover Effects
function initProductHovers() {
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        const image = card.querySelector('.product-image');
        const overlay = card.querySelector('.product-overlay');
        
        if (!image || !overlay) return;
        
        card.addEventListener('mouseenter', () => {
            // Add hover class for additional animations
            card.classList.add('hovered');
            
            // Animate image scale
            image.style.transform = 'scale(1.1)';
            
            // Show overlay with stagger effect
            const actions = overlay.querySelectorAll('.btn');
            actions.forEach((btn, index) => {
                setTimeout(() => {
                    btn.style.transform = 'translateY(0)';
                    btn.style.opacity = '1';
                }, index * 100);
            });
        });
        
        card.addEventListener('mouseleave', () => {
            card.classList.remove('hovered');
            
            // Reset image scale
            image.style.transform = 'scale(1)';
            
            // Hide overlay
            const actions = overlay.querySelectorAll('.btn');
            actions.forEach(btn => {
                btn.style.transform = 'translateY(20px)';
                btn.style.opacity = '0';
            });
        });
        
        // Initialize button positions
        const actions = overlay.querySelectorAll('.btn');
        actions.forEach(btn => {
            btn.style.transform = 'translateY(20px)';
            btn.style.opacity = '0';
            btn.style.transition = 'all 0.3s ease';
        });
    });
}

// Smooth reveal animations for sections
function initSectionReveals() {
    const sections = document.querySelectorAll('.section');
    
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
                
                // Stagger child animations
                const children = entry.target.querySelectorAll('.fade-in');
                children.forEach((child, index) => {
                    setTimeout(() => {
                        child.classList.add('visible');
                    }, index * 150);
                });
            }
        });
    }, observerOptions);
    
    sections.forEach(section => {
        observer.observe(section);
    });
}

// Initialize section reveals
initSectionReveals();

// Testimonial carousel (if needed)
function initTestimonialCarousel() {
    const testimonials = document.querySelectorAll('.testimonial-card');
    
    if (testimonials.length <= 3) return; // No need for carousel if 3 or fewer items
    
    let currentTestimonial = 0;
    const testimonialsPerView = 3;
    
    function showTestimonials(startIndex) {
        testimonials.forEach((testimonial, index) => {
            if (index >= startIndex && index < startIndex + testimonialsPerView) {
                testimonial.style.display = 'block';
                testimonial.classList.add('visible');
            } else {
                testimonial.style.display = 'none';
                testimonial.classList.remove('visible');
            }
        });
    }
    
    function nextTestimonials() {
        currentTestimonial = (currentTestimonial + testimonialsPerView) % testimonials.length;
        showTestimonials(currentTestimonial);
    }
    
    function prevTestimonials() {
        currentTestimonial = (currentTestimonial - testimonialsPerView + testimonials.length) % testimonials.length;
        showTestimonials(currentTestimonial);
    }
    
    // Auto-rotate testimonials
    setInterval(nextTestimonials, 8000);
    
    // Initial display
    showTestimonials(0);
}

// Initialize testimonial carousel
initTestimonialCarousel();

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => {
        imageObserver.observe(img);
    });
}

// Initialize lazy loading
initLazyLoading();

// Performance optimization: Reduce animations on low-end devices
function optimizeForPerformance() {
    // Check if device prefers reduced motion
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        document.body.classList.add('reduced-motion');
        return;
    }
    
    // Simple performance check
    const start = performance.now();
    requestAnimationFrame(() => {
        const end = performance.now();
        const frameDuration = end - start;
        
        // If frame takes too long, reduce animations
        if (frameDuration > 20) {
            document.body.classList.add('reduced-motion');
        }
    });
}

// Initialize performance optimization
optimizeForPerformance();

// Add CSS for reduced motion
const style = document.createElement('style');
style.textContent = `
    .reduced-motion * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
`;
document.head.appendChild(style);
