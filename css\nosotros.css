/* UVAMAYU - Nosotros Page Styles */

/* Page Hero */
.page-hero {
    position: relative;
    height: 70vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    filter: brightness(0.6);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(132, 20, 42, 0.6) 100%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: var(--color-white);
    max-width: 800px;
    padding: 2rem;
}

.hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
    color: var(--color-white);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

/* Breadcrumb */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    margin-top: 1rem;
}

.breadcrumb a {
    color: var(--color-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb a:hover {
    color: var(--color-white);
}

.breadcrumb .separator {
    color: rgba(255, 255, 255, 0.6);
}

.breadcrumb .current {
    color: var(--color-white);
}

/* Historia Section */
.historia-section {
    padding: 6rem 0;
}

.historia-content h2 {
    margin-bottom: 2rem;
    font-size: 2.5rem;
}

.historia-content .text-large {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    color: var(--color-primary);
    font-weight: 500;
}

.historia-content p {
    margin-bottom: 1.5rem;
    line-height: 1.7;
    color: var(--color-dark-gray);
}

.timeline-years {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.year-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--color-light-gray);
    border-radius: 1rem;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.year-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-secondary);
    transform: scaleX(0);
    transition: transform var(--transition-medium);
}

.year-item:hover::before {
    transform: scaleX(1);
}

.year-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.year {
    font-size: 2rem;
    font-weight: 800;
    color: var(--color-primary);
    margin-bottom: 0.5rem;
    font-family: var(--font-primary);
}

.year-description {
    color: var(--color-medium-gray);
    font-weight: 500;
}

.historia-image {
    position: relative;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
}

.historia-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.historia-image:hover img {
    transform: scale(1.05);
}

.image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--color-white);
    padding: 2rem;
    transform: translateY(100%);
    transition: transform var(--transition-medium);
}

.historia-image:hover .image-caption {
    transform: translateY(0);
}

.image-caption h4 {
    color: var(--color-secondary);
    margin-bottom: 0.5rem;
}

/* Valores Section */
.valores-section {
    padding: 6rem 0;
}

.section-header {
    margin-bottom: 4rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--color-medium-gray);
    max-width: 600px;
    margin: 0 auto;
}

.valores-grid {
    gap: 2rem;
}

.valor-card {
    background: var(--color-white);
    padding: 2.5rem 2rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.valor-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    opacity: 0;
    transition: opacity var(--transition-medium);
    z-index: 1;
}

.valor-card:hover::before {
    opacity: 0.05;
}

.valor-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.valor-card > * {
    position: relative;
    z-index: 2;
}

.valor-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--color-white);
    font-size: 2rem;
    transition: all var(--transition-medium);
}

.valor-card:hover .valor-icon {
    background: var(--gradient-secondary);
    transform: scale(1.1);
}

.valor-card h3 {
    color: var(--color-primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.valor-card p {
    color: var(--color-medium-gray);
    line-height: 1.6;
}

/* Equipo Section */
.equipo-section {
    padding: 6rem 0;
}

.equipo-grid {
    gap: 3rem;
}

.miembro-card {
    background: var(--color-white);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
}

.miembro-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.miembro-image {
    height: 300px;
    overflow: hidden;
    position: relative;
}

.miembro-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.miembro-card:hover .miembro-image img {
    transform: scale(1.1);
}

.miembro-info {
    padding: 2rem;
    text-align: center;
}

.miembro-info h3 {
    color: var(--color-primary);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.miembro-cargo {
    color: var(--color-secondary);
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.9rem;
}

.miembro-info p {
    color: var(--color-medium-gray);
    line-height: 1.6;
}

/* Reconocimientos Section */
.reconocimientos-section {
    padding: 6rem 0;
}

.reconocimientos-grid {
    gap: 2rem;
}

.reconocimiento-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(196, 154, 82, 0.3);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: all var(--transition-medium);
}

.reconocimiento-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
    border-color: var(--color-secondary);
}

.reconocimiento-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: var(--color-white);
    font-size: 1.5rem;
    transition: all var(--transition-medium);
}

.reconocimiento-card:hover .reconocimiento-icon {
    transform: scale(1.1);
    background: var(--color-secondary);
}

.reconocimiento-card h4 {
    color: var(--color-white);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.reconocimiento-card p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--color-light-gray) 0%, var(--color-white) 100%);
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    margin-bottom: 1.5rem;
    color: var(--color-primary);
}

.cta-content .text-large {
    margin-bottom: 3rem;
    color: var(--color-medium-gray);
}

.cta-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-lg {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
    min-width: 200px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .timeline-years {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .valores-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .reconocimientos-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .page-hero {
        height: 60vh;
        min-height: 400px;
    }
    
    .hero-content {
        padding: 1rem;
    }
    
    .historia-section,
    .valores-section,
    .equipo-section,
    .reconocimientos-section,
    .cta-section {
        padding: 4rem 0;
    }
    
    .timeline-years {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .valores-grid {
        grid-template-columns: 1fr;
    }
    
    .equipo-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .reconocimientos-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-lg {
        width: 100%;
        max-width: 300px;
    }
    
    .miembro-image {
        height: 250px;
    }
    
    .valor-card {
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .historia-content h2 {
        font-size: 2rem;
    }
    
    .year {
        font-size: 1.5rem;
    }
    
    .year-item {
        padding: 1rem;
    }
    
    .valor-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .reconocimiento-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .miembro-info {
        padding: 1.5rem;
    }
    
    .reconocimiento-card {
        padding: 1.5rem;
    }
}
