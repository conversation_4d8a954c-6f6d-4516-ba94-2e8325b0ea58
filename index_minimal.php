<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔍 Cargando versión mínima...<br>";

// Solo cargar config.php directamente
include 'config.php';
echo "✅ Config cargado<br>";

// Probar función básica
$productos = getProducts(null, 1, 3);
echo "✅ Productos obtenidos: " . count($productos) . "<br>";
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UVAMAYU - Prueba Mínima</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 40px; 
            border-radius: 10px; 
            box-shadow: 0 4px 20px rgba(0,0,0,0.1); 
        }
        h1 { 
            color: #84142A; 
            text-align: center; 
            margin-bottom: 30px; 
        }
        .product { 
            border: 1px solid #ddd; 
            padding: 20px; 
            margin: 10px 0; 
            border-radius: 8px; 
            background: #fafafa; 
        }
        .btn { 
            background: #84142A; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 5px; 
            display: inline-block; 
            margin: 5px; 
        }
        .success { color: green; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍷 UVAMAYU - Versión Mínima</h1>
        
        <div class="success">
            ✅ Sitio web funcionando correctamente
        </div>
        
        <h2>Productos Destacados</h2>
        <?php if (!empty($productos)): ?>
            <?php foreach ($productos as $producto): ?>
                <div class="product">
                    <h3><?php echo htmlspecialchars($producto['nombre']); ?></h3>
                    <p><strong>Categoría:</strong> <?php echo $producto['categoria']; ?></p>
                    <p><strong>Precio:</strong> <?php echo formatPrice($producto['precio_unitario']); ?></p>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p>No hay productos disponibles</p>
        <?php endif; ?>
        
        <h2>Enlaces</h2>
        <a href="index.php" class="btn">Página Principal</a>
        <a href="productos.php" class="btn">Productos</a>
        <a href="debug.php" class="btn">Diagnóstico</a>
        <a href="test.php" class="btn">Pruebas</a>
        
        <h2>Información</h2>
        <p><strong>Marca:</strong> <?php echo BRAND_NAME; ?></p>
        <p><strong>Fecha:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        <p><strong>PHP:</strong> <?php echo phpversion(); ?></p>
    </div>
</body>
</html>
