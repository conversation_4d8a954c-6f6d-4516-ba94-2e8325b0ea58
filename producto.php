<?php
// Verificar que se proporcione un ID
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: productos.php');
    exit;
}

$product_id = (int)$_GET['id'];

include 'config.php';

// Obtener el producto
$producto = getProductById($product_id);

if (!$producto) {
    header('Location: productos.php');
    exit;
}

$page_title = $producto['nombre'];
$page_description = $producto['descripcion'] ?: "Descubre {$producto['nombre']}, un {$producto['categoria']} artesanal de UVAMAYU con calidad premium y tradición peruana.";
$additional_css = ['css/producto.css'];
$additional_js = ['js/producto.js'];

include 'includes/header.php';

// Obtener productos relacionados
$productos_relacionados = getProducts($producto['categoria'], null, 4);
// Filtrar el producto actual
$productos_relacionados = array_filter($productos_relacionados, function($p) use ($product_id) {
    return $p['id'] != $product_id;
});
$productos_relacionados = array_slice($productos_relacionados, 0, 3);
?>

<!-- Breadcrumb -->
<section class="breadcrumb-section">
    <div class="container">
        <nav class="breadcrumb">
            <a href="index.php">Inicio</a>
            <span class="separator">/</span>
            <a href="productos.php">Productos</a>
            <span class="separator">/</span>
            <a href="productos.php?categoria=<?php echo $producto['categoria']; ?>"><?php echo $producto['categoria']; ?>s</a>
            <span class="separator">/</span>
            <span class="current"><?php echo htmlspecialchars($producto['nombre']); ?></span>
        </nav>
    </div>
</section>

<!-- Product Detail Section -->
<section class="product-detail-section section">
    <div class="container">
        <div class="product-detail-grid">
            <!-- Product Images -->
            <div class="product-images">
                <div class="main-image-container">
                    <img src="<?php echo $producto['imagen_principal'] ?: 'assets/placeholder-product.jpg'; ?>" 
                         alt="<?php echo htmlspecialchars($producto['nombre']); ?>" 
                         class="main-image" 
                         id="mainImage">
                    
                    <?php if ($producto['destacado']): ?>
                        <div class="product-badge featured">
                            <i class="fas fa-star"></i>
                            Destacado
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($producto['stock'] <= 0): ?>
                        <div class="product-badge out-of-stock">
                            <i class="fas fa-exclamation-triangle"></i>
                            Agotado
                        </div>
                    <?php endif; ?>
                    
                    <button class="zoom-btn" onclick="openImageModal('<?php echo $producto['imagen_principal']; ?>')">
                        <i class="fas fa-search-plus"></i>
                    </button>
                </div>
                
                <!-- Thumbnail Gallery (if you have multiple images) -->
                <div class="thumbnail-gallery">
                    <div class="thumbnail active" onclick="changeMainImage('<?php echo $producto['imagen_principal']; ?>')">
                        <img src="<?php echo $producto['imagen_principal'] ?: 'assets/placeholder-product.jpg'; ?>" 
                             alt="Vista principal">
                    </div>
                    <!-- Add more thumbnails here if you have additional product images -->
                </div>
            </div>
            
            <!-- Product Information -->
            <div class="product-info">
                <div class="product-header">
                    <div class="product-category"><?php echo $producto['categoria']; ?></div>
                    <h1 class="product-title"><?php echo htmlspecialchars($producto['nombre']); ?></h1>
                    <p class="product-type"><?php echo htmlspecialchars($producto['tipo']); ?></p>
                </div>
                
                <div class="product-pricing">
                    <div class="price-main"><?php echo formatPrice($producto['precio_unitario']); ?></div>
                    <?php if ($producto['precio_caja']): ?>
                        <div class="price-bulk">
                            <span class="price-label">Precio por caja:</span>
                            <span class="price-value"><?php echo formatPrice($producto['precio_caja']); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="product-stock">
                    <?php if ($producto['stock'] > 0): ?>
                        <span class="stock-available">
                            <i class="fas fa-check-circle"></i>
                            Disponible
                        </span>
                    <?php else: ?>
                        <span class="stock-unavailable">
                            <i class="fas fa-times-circle"></i>
                            No disponible
                        </span>
                    <?php endif; ?>
                </div>
                
                <?php if ($producto['descripcion']): ?>
                    <div class="product-description">
                        <h3>Descripción</h3>
                        <p><?php echo nl2br(htmlspecialchars($producto['descripcion'])); ?></p>
                    </div>
                <?php endif; ?>
                
                <div class="product-details">
                    <h3>Detalles del Producto</h3>
                    <div class="details-grid">
                        <?php if ($producto['varietal']): ?>
                            <div class="detail-item">
                                <i class="fas fa-grape-cluster"></i>
                                <div class="detail-content">
                                    <span class="detail-label">Varietal</span>
                                    <span class="detail-value"><?php echo htmlspecialchars($producto['varietal']); ?></span>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($producto['graduacion']): ?>
                            <div class="detail-item">
                                <i class="fas fa-percentage"></i>
                                <div class="detail-content">
                                    <span class="detail-label">Graduación</span>
                                    <span class="detail-value"><?php echo htmlspecialchars($producto['graduacion']); ?></span>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="detail-item">
                            <i class="fas fa-wine-bottle"></i>
                            <div class="detail-content">
                                <span class="detail-label">Tamaño</span>
                                <span class="detail-value"><?php echo $product_sizes[$producto['tamanio']]; ?></span>
                            </div>
                        </div>
                        
                        <?php if ($producto['aroma']): ?>
                            <div class="detail-item">
                                <i class="fas fa-nose"></i>
                                <div class="detail-content">
                                    <span class="detail-label">Aroma</span>
                                    <span class="detail-value"><?php echo htmlspecialchars($producto['aroma']); ?></span>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($producto['maridaje']): ?>
                            <div class="detail-item">
                                <i class="fas fa-utensils"></i>
                                <div class="detail-content">
                                    <span class="detail-label">Maridaje</span>
                                    <span class="detail-value"><?php echo htmlspecialchars($producto['maridaje']); ?></span>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="product-actions">
                    <button onclick="sendWhatsApp('Hola, me interesa el producto: <?php echo htmlspecialchars($producto['nombre']); ?> - <?php echo formatPrice($producto['precio_unitario']); ?>. ¿Podrían darme más información?')" 
                            class="btn btn-primary btn-lg">
                        <i class="fab fa-whatsapp"></i>
                        Consultar por WhatsApp
                    </button>
                    
                    <button onclick="sendEmail('Consulta sobre <?php echo htmlspecialchars($producto['nombre']); ?>', 'Hola,\n\nMe gustaría recibir más información sobre el producto:\n\nProducto: <?php echo htmlspecialchars($producto['nombre']); ?>\nPrecio: <?php echo formatPrice($producto['precio_unitario']); ?>\n\nGracias.')" 
                            class="btn btn-secondary btn-lg">
                        <i class="fas fa-envelope"></i>
                        Enviar Email
                    </button>
                    
                    <button onclick="shareProduct()" class="btn btn-outline btn-lg">
                        <i class="fas fa-share-alt"></i>
                        Compartir
                    </button>
                </div>
                
                <div class="product-guarantee">
                    <div class="guarantee-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Calidad garantizada</span>
                    </div>
                    <div class="guarantee-item">
                        <i class="fas fa-truck"></i>
                        <span>Envío seguro</span>
                    </div>
                    <div class="guarantee-item">
                        <i class="fas fa-award"></i>
                        <span>Producto artesanal</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Products Section -->
<?php if (!empty($productos_relacionados)): ?>
<section class="related-products-section section section-light">
    <div class="container">
        <div class="section-header text-center fade-in">
            <h2>Productos <span class="text-secondary">Relacionados</span></h2>
            <p class="section-subtitle">
                Descubre otros productos de nuestra categoría <?php echo $producto['categoria']; ?>
            </p>
        </div>
        
        <div class="related-products-grid grid grid-3">
            <?php foreach ($productos_relacionados as $related): ?>
                <div class="product-card fade-in">
                    <div class="product-image-container">
                        <img src="<?php echo $related['imagen_principal'] ?: 'assets/placeholder-product.jpg'; ?>" 
                             alt="<?php echo htmlspecialchars($related['nombre']); ?>" 
                             class="product-image">
                        
                        <div class="product-overlay">
                            <div class="product-actions">
                                <a href="producto.php?id=<?php echo $related['id']; ?>" 
                                   class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye"></i>
                                    Ver Detalles
                                </a>
                            </div>
                        </div>
                        
                        <?php if ($related['destacado']): ?>
                            <div class="product-badge featured">
                                <i class="fas fa-star"></i>
                                Destacado
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-info">
                        <div class="product-category"><?php echo $related['categoria']; ?></div>
                        <h3 class="product-title">
                            <a href="producto.php?id=<?php echo $related['id']; ?>">
                                <?php echo htmlspecialchars($related['nombre']); ?>
                            </a>
                        </h3>
                        <p class="product-type"><?php echo htmlspecialchars($related['tipo']); ?></p>
                        
                        <div class="product-pricing">
                            <div class="price-main"><?php echo formatPrice($related['precio_unitario']); ?></div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center fade-in">
            <a href="productos.php?categoria=<?php echo $producto['categoria']; ?>" class="btn btn-outline">
                <i class="fas fa-th-large"></i>
                Ver Todos los <?php echo $producto['categoria']; ?>s
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Image Modal -->
<div class="image-modal" id="imageModal">
    <div class="modal-overlay" onclick="closeImageModal()"></div>
    <div class="modal-content">
        <button class="modal-close" onclick="closeImageModal()">
            <i class="fas fa-times"></i>
        </button>
        <img src="" alt="" class="modal-image" id="modalImage">
    </div>
</div>

<?php include 'includes/footer.php'; ?>
