<?php
// Archivo de prueba para verificar la funcionalidad del sitio
$page_title = "Prueba del Sistema";
$page_description = "Página de prueba para verificar todas las funcionalidades de UVAMAYU";

include 'config.php';

// Verificar conexión a la base de datos
try {
    $pdo = getDBConnection();
    $db_status = "✅ Conexión exitosa";
} catch (Exception $e) {
    $db_status = "❌ Error: " . $e->getMessage();
}

// Verificar productos de prueba
$productos_test = [
    [
        'nombre' => 'Pisco Quebranta Premium',
        'categoria' => 'Pisco',
        'tipo' => 'Quebranta',
        'descripcion' => 'Pisco artesanal de uva Quebranta, destilado con métodos tradicionales.',
        'varietal' => 'Quebranta',
        'aroma' => 'Frutal con notas cítricas',
        'maridaje' => 'Ceviche, tiradito, anticuchos',
        'graduacion' => '42°',
        'tamanio' => '750',
        'precio_unitario' => 89.90,
        'precio_caja' => 510.00,
        'imagen_principal' => 'assets/productos/pisco-quebranta.jpg',
        'stock' => 25,
        'destacado' => 1,
        'activo' => 1
    ],
    [
        'nombre' => 'Vino Borgoña Reserva',
        'categoria' => 'Vino',
        'tipo' => 'Borgoña',
        'descripcion' => 'Vino tinto de cuerpo medio con notas frutales y especiadas.',
        'varietal' => 'Borgoña',
        'aroma' => 'Frutas rojas maduras',
        'maridaje' => 'Carnes rojas, quesos curados',
        'graduacion' => '13.5°',
        'tamanio' => '750',
        'precio_unitario' => 65.00,
        'precio_caja' => 390.00,
        'imagen_principal' => 'assets/productos/vino-borgona.jpg',
        'stock' => 18,
        'destacado' => 1,
        'activo' => 1
    ],
    [
        'nombre' => 'Pisco Italia Especial',
        'categoria' => 'Pisco',
        'tipo' => 'Italia',
        'descripcion' => 'Pisco aromático de uva Italia con perfil floral distintivo.',
        'varietal' => 'Italia',
        'aroma' => 'Floral con notas de rosa',
        'maridaje' => 'Postres, chocolates',
        'graduacion' => '40°',
        'tamanio' => '750',
        'precio_unitario' => 95.00,
        'precio_caja' => 570.00,
        'imagen_principal' => 'assets/productos/pisco-italia.jpg',
        'stock' => 12,
        'destacado' => 0,
        'activo' => 1
    ]
];

include 'includes/header.php';
?>

<style>
.test-container {
    max-width: 1000px;
    margin: 100px auto 50px;
    padding: 2rem;
    background: var(--color-white);
    border-radius: 1rem;
    box-shadow: var(--shadow-medium);
}

.test-section {
    margin-bottom: 3rem;
    padding: 2rem;
    border: 1px solid var(--color-light-gray);
    border-radius: 0.5rem;
}

.test-section h2 {
    color: var(--color-primary);
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--color-secondary);
    padding-bottom: 0.5rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: var(--color-light-gray);
    border-radius: 0.25rem;
}

.test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.test-card {
    padding: 1rem;
    border: 1px solid var(--color-light-gray);
    border-radius: 0.5rem;
    background: var(--color-white);
}

.success { color: #10b981; }
.error { color: #e74c3c; }
.warning { color: #f59e0b; }

.responsive-test {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.device-preview {
    border: 2px solid var(--color-light-gray);
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    background: var(--color-light-gray);
}

.device-preview.mobile {
    max-width: 375px;
    margin: 0 auto;
}

.device-preview.tablet {
    max-width: 768px;
    margin: 0 auto;
}

.device-preview.desktop {
    max-width: 1200px;
    margin: 0 auto;
}
</style>

<div class="test-container">
    <h1>🧪 Panel de Pruebas - UVAMAYU</h1>
    <p>Esta página verifica el funcionamiento correcto de todas las funcionalidades del sitio web.</p>

    <!-- Pruebas de Sistema -->
    <div class="test-section">
        <h2>🔧 Estado del Sistema</h2>
        
        <div class="status-item">
            <span>Conexión a Base de Datos:</span>
            <span class="<?php echo strpos($db_status, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $db_status; ?>
            </span>
        </div>
        
        <div class="status-item">
            <span>Archivos CSS:</span>
            <span class="<?php echo file_exists('css/style.css') ? 'success' : 'error'; ?>">
                <?php echo file_exists('css/style.css') ? '✅ Cargados' : '❌ No encontrados'; ?>
            </span>
        </div>
        
        <div class="status-item">
            <span>Archivos JavaScript:</span>
            <span class="<?php echo file_exists('js/main.js') ? 'success' : 'error'; ?>">
                <?php echo file_exists('js/main.js') ? '✅ Cargados' : '❌ No encontrados'; ?>
            </span>
        </div>
        
        <div class="status-item">
            <span>Imágenes de Assets:</span>
            <span class="<?php echo is_dir('assets') ? 'success' : 'error'; ?>">
                <?php echo is_dir('assets') ? '✅ Directorio encontrado' : '❌ Directorio no encontrado'; ?>
            </span>
        </div>
        
        <div class="status-item">
            <span>Configuración:</span>
            <span class="<?php echo defined('BRAND_NAME') ? 'success' : 'error'; ?>">
                <?php echo defined('BRAND_NAME') ? '✅ Configurado' : '❌ Error en config.php'; ?>
            </span>
        </div>
    </div>

    <!-- Pruebas de Funcionalidad -->
    <div class="test-section">
        <h2>⚙️ Pruebas de Funcionalidad</h2>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>🎨 Colores de Marca</h3>
                <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                    <div style="width: 50px; height: 50px; background: <?php echo COLOR_PRIMARY; ?>; border-radius: 50%;"></div>
                    <div style="width: 50px; height: 50px; background: <?php echo COLOR_SECONDARY; ?>; border-radius: 50%;"></div>
                    <div style="width: 50px; height: 50px; background: <?php echo COLOR_BLACK; ?>; border-radius: 50%;"></div>
                </div>
                <p><small>Primario, Secundario, Negro</small></p>
            </div>
            
            <div class="test-card">
                <h3>📱 Enlaces de Contacto</h3>
                <p><strong>WhatsApp:</strong> <?php echo CONTACT_WHATSAPP; ?></p>
                <p><strong>Email:</strong> <?php echo CONTACT_EMAIL; ?></p>
                <p><strong>Web:</strong> <?php echo CONTACT_WEBSITE; ?></p>
            </div>
            
            <div class="test-card">
                <h3>🍷 Categorías de Productos</h3>
                <?php foreach ($product_categories as $categoria => $info): ?>
                    <p><strong><?php echo $categoria; ?>:</strong> <?php echo count($info['tipos']); ?> tipos</p>
                <?php endforeach; ?>
            </div>
            
            <div class="test-card">
                <h3>🏷️ Atributos de Marca</h3>
                <ul style="font-size: 0.9rem;">
                    <?php foreach (array_slice($brand_attributes, 0, 3) as $attr): ?>
                        <li><?php echo $attr; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- Pruebas de Responsive -->
    <div class="test-section">
        <h2>📱 Pruebas Responsive</h2>
        <p>Verifica cómo se ve el sitio en diferentes dispositivos:</p>
        
        <div class="responsive-test">
            <div class="device-preview mobile">
                <h4>📱 Mobile (375px)</h4>
                <p>Navegación hamburguesa</p>
                <p>Cards apiladas</p>
                <p>Texto optimizado</p>
            </div>
            
            <div class="device-preview tablet">
                <h4>📱 Tablet (768px)</h4>
                <p>Grid de 2 columnas</p>
                <p>Navegación completa</p>
                <p>Imágenes medianas</p>
            </div>
            
            <div class="device-preview desktop">
                <h4>🖥️ Desktop (1200px+)</h4>
                <p>Grid de 3-4 columnas</p>
                <p>Efectos hover</p>
                <p>Imágenes grandes</p>
            </div>
        </div>
    </div>

    <!-- Pruebas de Productos -->
    <div class="test-section">
        <h2>🍾 Productos de Prueba</h2>
        <p>Datos de ejemplo para testing:</p>
        
        <div class="test-grid">
            <?php foreach ($productos_test as $producto): ?>
                <div class="test-card">
                    <h4><?php echo $producto['nombre']; ?></h4>
                    <p><strong>Categoría:</strong> <?php echo $producto['categoria']; ?></p>
                    <p><strong>Precio:</strong> <?php echo formatPrice($producto['precio_unitario']); ?></p>
                    <p><strong>Stock:</strong> <?php echo $producto['stock']; ?> unidades</p>
                    <p><strong>Destacado:</strong> <?php echo $producto['destacado'] ? 'Sí' : 'No'; ?></p>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Acciones de Prueba -->
    <div class="test-section">
        <h2>🧪 Acciones de Prueba</h2>
        
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-home"></i>
                Ir al Inicio
            </a>
            
            <a href="productos.php" class="btn btn-secondary">
                <i class="fas fa-wine-bottle"></i>
                Ver Productos
            </a>
            
            <button onclick="testAnimations()" class="btn btn-outline">
                <i class="fas fa-magic"></i>
                Probar Animaciones
            </button>
            
            <button onclick="testResponsive()" class="btn btn-outline">
                <i class="fas fa-mobile-alt"></i>
                Simular Mobile
            </button>
            
            <button onclick="testNotifications()" class="btn btn-outline">
                <i class="fas fa-bell"></i>
                Probar Notificaciones
            </button>
        </div>
    </div>
</div>

<script>
function testAnimations() {
    const cards = document.querySelectorAll('.test-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.animation = 'bounceIn 0.6s ease';
        }, index * 200);
    });
    showNotification('Animaciones ejecutadas correctamente', 'success');
}

function testResponsive() {
    const container = document.querySelector('.test-container');
    container.style.maxWidth = '375px';
    container.style.transition = 'all 0.5s ease';
    
    setTimeout(() => {
        container.style.maxWidth = '1000px';
    }, 3000);
    
    showNotification('Simulación responsive completada', 'info');
}

function testNotifications() {
    showNotification('Notificación de éxito', 'success');
    setTimeout(() => showNotification('Notificación de advertencia', 'warning'), 1000);
    setTimeout(() => showNotification('Notificación de error', 'error'), 2000);
    setTimeout(() => showNotification('Notificación de información', 'info'), 3000);
}

// Auto-test al cargar la página
document.addEventListener('DOMContentLoaded', function() {
    console.log('🧪 Página de pruebas cargada correctamente');
    console.log('📊 Estado del sistema verificado');
    console.log('🎨 Estilos aplicados correctamente');
    console.log('⚡ JavaScript funcionando');
});
</script>

<?php include 'includes/footer.php'; ?>
