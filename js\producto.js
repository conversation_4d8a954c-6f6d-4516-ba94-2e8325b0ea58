// UVAMAYU - Product Detail Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initImageGallery();
    initImageModal();
    initShareFunctionality();
    initProductActions();
    initScrollAnimations();
});

// Image Gallery Functionality
function initImageGallery() {
    const thumbnails = document.querySelectorAll('.thumbnail');
    const mainImage = document.getElementById('mainImage');
    
    if (!mainImage || thumbnails.length === 0) return;
    
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            const newImageSrc = this.onclick.toString().match(/'([^']+)'/)[1];
            changeMainImage(newImageSrc);
        });
    });
}

// Change Main Image Function
function changeMainImage(imageSrc) {
    const mainImage = document.getElementById('mainImage');
    const thumbnails = document.querySelectorAll('.thumbnail');
    
    if (!mainImage) return;
    
    // Add loading effect
    mainImage.style.opacity = '0.5';
    mainImage.style.transform = 'scale(0.95)';
    
    // Create new image to preload
    const newImg = new Image();
    newImg.onload = function() {
        mainImage.src = this.src;
        mainImage.style.opacity = '1';
        mainImage.style.transform = 'scale(1)';
        
        // Update active thumbnail
        thumbnails.forEach(thumb => thumb.classList.remove('active'));
        const activeThumb = Array.from(thumbnails).find(thumb => 
            thumb.onclick.toString().includes(imageSrc)
        );
        if (activeThumb) {
            activeThumb.classList.add('active');
        }
    };
    newImg.src = imageSrc;
}

// Image Modal Functionality
function initImageModal() {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    
    if (!modal || !modalImage) return;
    
    // Close modal on overlay click
    modal.addEventListener('click', function(e) {
        if (e.target === modal || e.target.classList.contains('modal-overlay')) {
            closeImageModal();
        }
    });
    
    // Close modal on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.classList.contains('active')) {
            closeImageModal();
        }
    });
    
    // Prevent modal content click from closing modal
    const modalContent = modal.querySelector('.modal-content');
    if (modalContent) {
        modalContent.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
}

// Open Image Modal Function
function openImageModal(imageSrc) {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    
    if (!modal || !modalImage) return;
    
    modalImage.src = imageSrc;
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
    
    // Add zoom functionality to modal image
    let isZoomed = false;
    modalImage.style.cursor = 'zoom-in';
    
    modalImage.onclick = function(e) {
        e.stopPropagation();
        if (!isZoomed) {
            this.style.transform = 'scale(1.5)';
            this.style.cursor = 'zoom-out';
            isZoomed = true;
        } else {
            this.style.transform = 'scale(1)';
            this.style.cursor = 'zoom-in';
            isZoomed = false;
        }
    };
}

// Close Image Modal Function
function closeImageModal() {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    
    if (!modal) return;
    
    modal.classList.remove('active');
    document.body.style.overflow = '';
    
    // Reset zoom
    if (modalImage) {
        modalImage.style.transform = 'scale(1)';
        modalImage.style.cursor = 'zoom-in';
    }
}

// Share Functionality
function initShareFunctionality() {
    // Check if Web Share API is supported
    if (navigator.share) {
        // Use native sharing if available
        window.shareProduct = function() {
            const productName = document.querySelector('.product-title').textContent;
            const productPrice = document.querySelector('.price-main').textContent;
            
            navigator.share({
                title: `${productName} - UVAMAYU`,
                text: `Mira este producto: ${productName} - ${productPrice}`,
                url: window.location.href
            }).catch(err => {
                console.log('Error sharing:', err);
                fallbackShare();
            });
        };
    } else {
        // Fallback sharing options
        window.shareProduct = fallbackShare;
    }
}

// Fallback Share Function
function fallbackShare() {
    const productName = document.querySelector('.product-title').textContent;
    const productPrice = document.querySelector('.price-main').textContent;
    const url = window.location.href;
    
    const shareText = `Mira este producto: ${productName} - ${productPrice}\n${url}`;
    
    // Create share modal
    const shareModal = document.createElement('div');
    shareModal.className = 'share-modal';
    shareModal.innerHTML = `
        <div class="share-overlay" onclick="closeShareModal()"></div>
        <div class="share-content">
            <h3>Compartir Producto</h3>
            <div class="share-options">
                <a href="https://wa.me/?text=${encodeURIComponent(shareText)}" 
                   target="_blank" 
                   class="share-option whatsapp">
                    <i class="fab fa-whatsapp"></i>
                    WhatsApp
                </a>
                <a href="https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}" 
                   target="_blank" 
                   class="share-option facebook">
                    <i class="fab fa-facebook"></i>
                    Facebook
                </a>
                <a href="https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}" 
                   target="_blank" 
                   class="share-option twitter">
                    <i class="fab fa-twitter"></i>
                    Twitter
                </a>
                <button onclick="copyToClipboard('${url}')" class="share-option copy">
                    <i class="fas fa-copy"></i>
                    Copiar Enlace
                </button>
            </div>
            <button onclick="closeShareModal()" class="close-share">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(shareModal);
    setTimeout(() => shareModal.classList.add('active'), 10);
    
    // Close modal function
    window.closeShareModal = function() {
        shareModal.classList.remove('active');
        setTimeout(() => document.body.removeChild(shareModal), 300);
    };
}

// Copy to Clipboard Function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Enlace copiado al portapapeles', 'success');
        closeShareModal();
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Enlace copiado al portapapeles', 'success');
        closeShareModal();
    });
}

// Product Actions Enhancement
function initProductActions() {
    const actionButtons = document.querySelectorAll('.product-actions .btn');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            // Track action for analytics (optional)
            const action = this.textContent.trim();
            console.log('Product action:', action);
        });
    });
}

// Scroll Animations for Product Details
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                
                // Special animation for detail items
                if (entry.target.classList.contains('product-details')) {
                    const detailItems = entry.target.querySelectorAll('.detail-item');
                    detailItems.forEach((item, index) => {
                        setTimeout(() => {
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, index * 100);
                    });
                }
                
                // Special animation for guarantee items
                if (entry.target.classList.contains('product-guarantee')) {
                    const guaranteeItems = entry.target.querySelectorAll('.guarantee-item');
                    guaranteeItems.forEach((item, index) => {
                        setTimeout(() => {
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, index * 150);
                    });
                }
            }
        });
    }, observerOptions);
    
    // Observe elements
    document.querySelectorAll('.product-details, .product-guarantee, .related-products-section').forEach(el => {
        observer.observe(el);
    });
    
    // Initialize detail items for animation
    document.querySelectorAll('.detail-item').forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'all 0.6s ease';
    });
    
    // Initialize guarantee items for animation
    document.querySelectorAll('.guarantee-item').forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'all 0.6s ease';
    });
}

// Sticky Product Info (for mobile)
function initStickyProductInfo() {
    if (window.innerWidth <= 768) {
        const productActions = document.querySelector('.product-actions');
        const productInfo = document.querySelector('.product-info');
        
        if (!productActions || !productInfo) return;
        
        let isSticky = false;
        
        window.addEventListener('scroll', () => {
            const productInfoRect = productInfo.getBoundingClientRect();
            const shouldBeSticky = productInfoRect.bottom < window.innerHeight;
            
            if (shouldBeSticky && !isSticky) {
                productActions.classList.add('sticky-actions');
                isSticky = true;
            } else if (!shouldBeSticky && isSticky) {
                productActions.classList.remove('sticky-actions');
                isSticky = false;
            }
        });
    }
}

// Initialize sticky product info
initStickyProductInfo();

// Add CSS for share modal and sticky actions
const style = document.createElement('style');
style.textContent = `
    .share-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .share-modal.active {
        opacity: 1;
        visibility: visible;
    }
    
    .share-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }
    
    .share-content {
        background: var(--color-white);
        border-radius: 1rem;
        padding: 2rem;
        max-width: 400px;
        width: 90%;
        position: relative;
        transform: scale(0.8);
        transition: transform 0.3s ease;
    }
    
    .share-modal.active .share-content {
        transform: scale(1);
    }
    
    .share-content h3 {
        color: var(--color-primary);
        margin-bottom: 1.5rem;
        text-align: center;
    }
    
    .share-options {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .share-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        border: 1px solid var(--color-light-gray);
        border-radius: 0.5rem;
        text-decoration: none;
        color: var(--color-dark-gray);
        transition: all 0.3s ease;
        background: none;
        cursor: pointer;
        font-family: inherit;
    }
    
    .share-option:hover {
        border-color: var(--color-primary);
        background: var(--color-light-gray);
    }
    
    .share-option.whatsapp:hover { border-color: #25D366; }
    .share-option.facebook:hover { border-color: #1877f2; }
    .share-option.twitter:hover { border-color: #1da1f2; }
    
    .share-option i {
        font-size: 1.5rem;
    }
    
    .close-share {
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 30px;
        height: 30px;
        border: none;
        background: var(--color-light-gray);
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .close-share:hover {
        background: var(--color-primary);
        color: var(--color-white);
    }
    
    .sticky-actions {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: var(--color-white);
        padding: 1rem;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        z-index: 100;
        border-top: 1px solid var(--color-light-gray);
    }
    
    .sticky-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    @media (max-width: 480px) {
        .share-options {
            grid-template-columns: 1fr;
        }
        
        .share-content {
            padding: 1.5rem;
        }
    }
`;
document.head.appendChild(style);
