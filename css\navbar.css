/* UVAMAYU - Navbar Styles */

.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all var(--transition-medium);
    border-bottom: 1px solid rgba(196, 154, 82, 0.2);
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.98);
    box-shadow: var(--shadow-medium);
}

.nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    position: relative;
}

/* Logo */
.nav-logo {
    z-index: 1001;
}

.logo-link {
    text-decoration: none;
    display: block;
}

.logo-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.brand-name {
    font-family: var(--font-primary);
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--color-white);
    letter-spacing: 0.05em;
    line-height: 1;
    transition: color var(--transition-fast);
}

.brand-subtitle {
    font-family: var(--font-accent);
    font-size: 0.9rem;
    color: var(--color-secondary);
    font-style: italic;
    letter-spacing: 0.1em;
    margin-top: -0.2rem;
}

.logo-link:hover .brand-name {
    color: var(--color-secondary);
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 2rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: var(--color-white);
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    transition: all var(--transition-fast);
    border-radius: 0.5rem;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--color-secondary);
    transition: all var(--transition-medium);
    transform: translateX(-50%);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 80%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--color-secondary);
    background: rgba(196, 154, 82, 0.1);
}

.dropdown-icon {
    font-size: 0.8rem;
    transition: transform var(--transition-fast);
}

.nav-item:hover .dropdown-icon {
    transform: rotate(180deg);
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--color-black);
    border: 1px solid rgba(196, 154, 82, 0.3);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-heavy);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-medium);
    z-index: 1002;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.25rem;
    color: var(--color-white);
    text-decoration: none;
    font-size: 0.95rem;
    transition: all var(--transition-fast);
    border-bottom: 1px solid rgba(196, 154, 82, 0.1);
}

.dropdown-link:last-child {
    border-bottom: none;
}

.dropdown-link:hover {
    background: rgba(196, 154, 82, 0.1);
    color: var(--color-secondary);
    padding-left: 1.5rem;
}

.dropdown-link i {
    width: 16px;
    text-align: center;
    color: var(--color-secondary);
}

/* Contact Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 1001;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    text-decoration: none;
    font-size: 1.2rem;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transform: scale(0);
    transition: transform var(--transition-medium);
    z-index: -1;
}

.action-btn:hover::before {
    transform: scale(1);
}

.whatsapp-btn {
    color: #25D366;
    border: 2px solid #25D366;
}

.whatsapp-btn::before {
    background: #25D366;
}

.whatsapp-btn:hover {
    color: var(--color-white);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
}

.email-btn {
    color: var(--color-secondary);
    border: 2px solid var(--color-secondary);
}

.email-btn::before {
    background: var(--color-secondary);
}

.email-btn:hover {
    color: var(--color-white);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(196, 154, 82, 0.4);
}

/* Mobile Menu Toggle */
.nav-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 25px;
    cursor: pointer;
    z-index: 1001;
}

.toggle-line {
    width: 100%;
    height: 3px;
    background: var(--color-white);
    border-radius: 2px;
    transition: all var(--transition-medium);
    transform-origin: center;
}

.nav-toggle.active .toggle-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.nav-toggle.active .toggle-line:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .toggle-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-medium);
    z-index: 999;
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-list {
        gap: 1.5rem;
    }
    
    .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.95rem;
    }
}

@media (max-width: 768px) {
    .nav-toggle {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        top: 0;
        right: -100%;
        width: 300px;
        height: 100vh;
        background: var(--gradient-dark);
        padding: 6rem 2rem 2rem;
        transition: right var(--transition-medium);
        z-index: 1000;
        overflow-y: auto;
    }
    
    .nav-menu.active {
        right: 0;
    }
    
    .nav-list {
        flex-direction: column;
        gap: 0;
        width: 100%;
    }
    
    .nav-item {
        width: 100%;
        border-bottom: 1px solid rgba(196, 154, 82, 0.2);
    }
    
    .nav-link {
        width: 100%;
        padding: 1.25rem 0;
        justify-content: space-between;
        border-radius: 0;
    }
    
    .nav-link::before {
        display: none;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        background: rgba(196, 154, 82, 0.1);
        border: none;
        border-radius: 0;
        box-shadow: none;
        margin-top: 0;
        max-height: 0;
        overflow: hidden;
        transition: max-height var(--transition-medium);
    }
    
    .dropdown:hover .dropdown-menu,
    .dropdown.active .dropdown-menu {
        max-height: 200px;
    }
    
    .dropdown-link {
        padding: 1rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .nav-actions {
        position: absolute;
        bottom: 2rem;
        left: 2rem;
        right: 2rem;
        justify-content: center;
        gap: 1.5rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(196, 154, 82, 0.2);
    }
    
    .action-btn {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }
    
    .brand-name {
        font-size: 1.6rem;
    }
    
    .brand-subtitle {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .nav-wrapper {
        padding: 0.75rem 0;
    }
    
    .nav-menu {
        width: 280px;
        padding: 5rem 1.5rem 1.5rem;
    }
    
    .brand-name {
        font-size: 1.4rem;
    }
    
    .brand-subtitle {
        font-size: 0.75rem;
    }
    
    .action-btn {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

/* Animation for mobile menu items */
@media (max-width: 768px) {
    .nav-menu.active .nav-item {
        animation: slideInRight 0.3s ease forwards;
    }
    
    .nav-menu.active .nav-item:nth-child(1) { animation-delay: 0.1s; }
    .nav-menu.active .nav-item:nth-child(2) { animation-delay: 0.2s; }
    .nav-menu.active .nav-item:nth-child(3) { animation-delay: 0.3s; }
    .nav-menu.active .nav-item:nth-child(4) { animation-delay: 0.4s; }
    .nav-menu.active .nav-item:nth-child(5) { animation-delay: 0.5s; }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Body scroll lock when mobile menu is open */
body.nav-open {
    overflow: hidden;
}
