/* UVAMAYU - Product Detail Page Styles */

/* Breadcrumb Section */
.breadcrumb-section {
    background: var(--color-light-gray);
    padding: 1.5rem 0;
    margin-top: 80px; /* Account for fixed navbar */
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
}

.breadcrumb a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb a:hover {
    color: var(--color-secondary);
}

.breadcrumb .separator {
    color: var(--color-medium-gray);
}

.breadcrumb .current {
    color: var(--color-dark-gray);
    font-weight: 500;
}

/* Product Detail Section */
.product-detail-section {
    padding: 3rem 0;
}

.product-detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

/* Product Images */
.product-images {
    position: sticky;
    top: 100px;
}

.main-image-container {
    position: relative;
    background: var(--color-white);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    margin-bottom: 1rem;
}

.main-image {
    width: 100%;
    height: 500px;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.main-image:hover {
    transform: scale(1.05);
}

.zoom-btn {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 45px;
    height: 45px;
    background: rgba(0, 0, 0, 0.7);
    color: var(--color-white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all var(--transition-medium);
    opacity: 0;
    transform: scale(0.8);
}

.main-image-container:hover .zoom-btn {
    opacity: 1;
    transform: scale(1);
}

.zoom-btn:hover {
    background: var(--color-primary);
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 2;
}

.product-badge.featured {
    background: var(--gradient-secondary);
    color: var(--color-white);
}

.product-badge.out-of-stock {
    background: #e74c3c;
    color: var(--color-white);
}

/* Thumbnail Gallery */
.thumbnail-gallery {
    display: flex;
    gap: 0.75rem;
    overflow-x: auto;
    padding: 0.5rem 0;
}

.thumbnail {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 0.5rem;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all var(--transition-medium);
}

.thumbnail.active {
    border-color: var(--color-primary);
    box-shadow: var(--shadow-medium);
}

.thumbnail:hover {
    border-color: var(--color-secondary);
    transform: translateY(-2px);
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Product Information */
.product-info {
    padding: 1rem 0;
}

.product-header {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--color-light-gray);
}

.product-category {
    color: var(--color-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.product-title {
    font-size: 2.5rem;
    color: var(--color-primary);
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.product-type {
    color: var(--color-medium-gray);
    font-size: 1.1rem;
    font-style: italic;
    margin-bottom: 1rem;
}

/* Product Pricing */
.product-pricing {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--color-light-gray);
    border-radius: 1rem;
    border-left: 4px solid var(--color-primary);
}

.price-main {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--color-primary);
    margin-bottom: 0.5rem;
    font-family: var(--font-primary);
}

.price-bulk {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.price-label {
    color: var(--color-medium-gray);
}

.price-value {
    color: var(--color-secondary);
    font-weight: 600;
}

/* Product Stock */
.product-stock {
    margin-bottom: 2rem;
}

.stock-available {
    color: #10b981;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stock-unavailable {
    color: #e74c3c;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Product Description */
.product-description {
    margin-bottom: 2rem;
}

.product-description h3 {
    color: var(--color-primary);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.product-description p {
    color: var(--color-dark-gray);
    line-height: 1.7;
    font-size: 1rem;
}

/* Product Details */
.product-details {
    margin-bottom: 2rem;
}

.product-details h3 {
    color: var(--color-primary);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

.details-grid {
    display: grid;
    gap: 1rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--color-white);
    border: 1px solid var(--color-light-gray);
    border-radius: 0.5rem;
    transition: all var(--transition-medium);
}

.detail-item:hover {
    border-color: var(--color-secondary);
    box-shadow: var(--shadow-light);
    transform: translateY(-2px);
}

.detail-item i {
    color: var(--color-secondary);
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.detail-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-label {
    font-size: 0.85rem;
    color: var(--color-medium-gray);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.detail-value {
    font-size: 1rem;
    color: var(--color-dark-gray);
    font-weight: 600;
}

/* Product Actions */
.product-actions {
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-actions .btn {
    justify-content: center;
    font-weight: 600;
    padding: 1.25rem 2rem;
    font-size: 1.1rem;
}

/* Product Guarantee */
.product-guarantee {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    padding: 1.5rem;
    background: var(--gradient-dark);
    border-radius: 1rem;
    color: var(--color-white);
}

.guarantee-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.5rem;
    padding: 1rem;
}

.guarantee-item i {
    color: var(--color-secondary);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.guarantee-item span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Related Products Section */
.related-products-section {
    padding: 4rem 0;
}

.section-header {
    margin-bottom: 3rem;
}

.section-subtitle {
    color: var(--color-medium-gray);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.related-products-grid {
    margin-bottom: 3rem;
}

/* Image Modal */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-medium);
}

.image-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.modal-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close {
    position: absolute;
    top: -50px;
    right: 0;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    color: var(--color-white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all var(--transition-medium);
    backdrop-filter: blur(10px);
}

.modal-close:hover {
    background: var(--color-primary);
    transform: scale(1.1);
}

.modal-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 0.5rem;
    box-shadow: var(--shadow-heavy);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .product-detail-grid {
        gap: 3rem;
    }

    .main-image {
        height: 400px;
    }

    .product-title {
        font-size: 2rem;
    }

    .price-main {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .breadcrumb-section {
        padding: 1rem 0;
    }

    .product-detail-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .product-images {
        position: static;
    }

    .main-image {
        height: 350px;
    }

    .product-title {
        font-size: 1.75rem;
    }

    .price-main {
        font-size: 1.75rem;
    }

    .product-actions {
        gap: 0.75rem;
    }

    .product-actions .btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .guarantee-item {
        padding: 0.75rem;
    }

    .guarantee-item i {
        font-size: 1.25rem;
    }

    .guarantee-item span {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .breadcrumb {
        font-size: 0.85rem;
        flex-wrap: wrap;
    }

    .product-detail-section {
        padding: 2rem 0;
    }

    .main-image {
        height: 300px;
    }

    .product-title {
        font-size: 1.5rem;
    }

    .price-main {
        font-size: 1.5rem;
    }

    .product-pricing {
        padding: 1rem;
    }

    .details-grid {
        gap: 0.75rem;
    }

    .detail-item {
        padding: 0.75rem;
    }

    .product-guarantee {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .thumbnail {
        width: 60px;
        height: 60px;
    }

    .modal-close {
        top: -40px;
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}
