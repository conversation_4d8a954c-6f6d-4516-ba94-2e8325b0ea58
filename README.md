# 🍷 UVAMAYU - Reserva del Sol

Sitio web premium para UVAMAYU, marca peruana de piscos y vinos artesanales. Diseño elegante, responsive y con animaciones suaves que reflejan la calidad y tradición de la marca.

## ✨ Características Principales

### 🎨 Diseño Premium
- **Paleta de colores elegante**: <PERSON><PERSON> (#84142A), Oro viejo (#C49A52), Negro profundo
- **Tipografía sofisticada**: Playfair Display, Inter, Cormorant Garamond
- **Animaciones suaves**: Efectos de scroll, transiciones, hover effects
- **Responsive design**: Optimizado para móviles, tablets y desktop

### 🛠️ Funcionalidades
- **Catálogo de productos**: Filtros por categoría, búsqueda, vista grid/lista
- **Detalle de productos**: Galería de imágenes, información completa, contacto directo
- **Integración WhatsApp**: Enlaces directos para consultas y pedidos
- **Formularios de contacto**: Email automático y notificaciones
- **SEO optimizado**: Meta tags, Open Graph, estructura semántica

### 🚀 Tecnologías
- **Frontend**: HTML5, CSS3, JavaScript ES6+
- **Backend**: PHP 7.4+
- **Base de datos**: MySQL
- **Responsive**: CSS Grid, Flexbox
- **Animaciones**: CSS Animations, Intersection Observer API
- **Accesibilidad**: ARIA labels, navegación por teclado

## 📁 Estructura del Proyecto

```
uvamayu/
├── assets/                 # Imágenes y recursos
│   ├── banners/           # Imágenes del slider principal
│   ├── ambiente/          # Fotos de ambiente
│   ├── historia/          # Imágenes históricas
│   └── secciones/         # Imágenes de secciones
├── css/                   # Archivos de estilos
│   ├── style.css          # Estilos principales
│   ├── navbar.css         # Navegación
│   ├── home.css           # Página principal
│   ├── productos.css      # Catálogo de productos
│   ├── producto.css       # Detalle de producto
│   └── components.css     # Componentes reutilizables
├── js/                    # JavaScript
│   ├── main.js            # Funcionalidades principales
│   ├── home.js            # Página principal
│   ├── productos.js       # Catálogo
│   └── producto.js        # Detalle de producto
├── includes/              # Archivos PHP reutilizables
│   ├── header.php         # Cabecera y navegación
│   └── footer.php         # Pie de página
├── config.php             # Configuración principal
├── index.php              # Página principal
├── productos.php          # Catálogo de productos
├── producto.php           # Detalle de producto
├── test.php               # Página de pruebas
└── README.md              # Este archivo
```

## 🚀 Instalación y Configuración

### Requisitos Previos
- Servidor web (Apache/Nginx)
- PHP 7.4 o superior
- MySQL 5.7 o superior
- Extensiones PHP: PDO, PDO_MySQL

### Paso 1: Configurar Base de Datos
```sql
CREATE DATABASE uvamayu_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE uvamayu_db;

CREATE TABLE IF NOT EXISTS productos (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nombre VARCHAR(100) NOT NULL,
  categoria ENUM('Pisco','Vino') NOT NULL,
  tipo VARCHAR(50) NOT NULL,
  descripcion TEXT,
  varietal VARCHAR(100),
  aroma VARCHAR(100),
  maridaje VARCHAR(100),
  graduacion VARCHAR(20),
  tamanio ENUM('100','750','3750') NOT NULL,
  precio_unitario DECIMAL(8,2) NOT NULL,
  precio_caja DECIMAL(8,2),
  imagen_principal VARCHAR(255),
  stock INT DEFAULT 0,
  destacado BOOL DEFAULT 0,
  activo BOOL DEFAULT 1
);
```

### Paso 2: Configurar Conexión
Edita `config.php` con tus datos de base de datos:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'uvamayu_db');
define('DB_USER', 'tu_usuario');
define('DB_PASS', 'tu_contraseña');
```

### Paso 3: Subir Archivos
1. Sube todos los archivos a tu servidor web
2. Asegúrate de que las carpetas tengan permisos de lectura
3. Verifica que las imágenes estén en la carpeta `assets/`

### Paso 4: Probar Instalación
Visita `tu-dominio.com/test.php` para verificar que todo funciona correctamente.

## 🎨 Personalización

### Colores de Marca
Los colores están definidos en `config.php` y `css/style.css`:
```php
define('COLOR_PRIMARY', '#84142A');    // Vino tinto
define('COLOR_SECONDARY', '#C49A52');  // Oro viejo
define('COLOR_BLACK', '#000000');      // Negro profundo
```

### Información de Contacto
Actualiza en `config.php`:
```php
define('CONTACT_EMAIL', '<EMAIL>');
define('CONTACT_WHATSAPP', '+51 924 352 679');
define('CONTACT_WEBSITE', 'www.uvamayu.com');
```

### Redes Sociales
```php
define('URL_INSTAGRAM', 'https://instagram.com/uvamayu');
define('URL_FACEBOOK', 'https://facebook.com/uvamayu');
define('URL_TIKTOK', 'https://tiktok.com/@uvamayu');
```

## 📱 Responsive Design

El sitio está optimizado para:
- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px+

### Breakpoints Principales
```css
@media (max-width: 480px)  { /* Mobile pequeño */ }
@media (max-width: 768px)  { /* Mobile/Tablet */ }
@media (max-width: 1024px) { /* Tablet/Desktop pequeño */ }
```

## 🎭 Animaciones y Efectos

### Animaciones Disponibles
- **Fade In**: Aparición suave
- **Slide**: Deslizamiento desde los lados
- **Scale**: Escalado suave
- **Bounce**: Efecto rebote
- **Float**: Flotación sutil
- **Magnetic**: Efecto magnético en botones

### Clases de Animación
```css
.fade-in          /* Aparición al hacer scroll */
.animate-scale    /* Escalado */
.animate-slide-left /* Deslizar desde izquierda */
.hover-lift       /* Elevación al hover */
.entrance-bounce  /* Entrada con rebote */
```

## 🔧 Funcionalidades Avanzadas

### Filtros de Productos
- Filtro por categoría (Pisco/Vino)
- Filtro por tipo de producto
- Búsqueda por texto
- Ordenamiento por precio/nombre
- Vista grid/lista

### Integración WhatsApp
```javascript
function sendWhatsApp(message) {
    const phone = '51924352679';
    const url = `https://wa.me/${phone}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
}
```

### Galería de Imágenes
- Modal con zoom
- Navegación por teclado
- Thumbnails
- Lazy loading

## 🧪 Testing

### Página de Pruebas
Visita `/test.php` para:
- Verificar conexión a base de datos
- Probar animaciones
- Simular responsive
- Verificar notificaciones

### Datos de Prueba
```sql
INSERT INTO productos (nombre, categoria, tipo, descripcion, precio_unitario, tamanio, destacado, activo) VALUES
('Pisco Quebranta Premium', 'Pisco', 'Quebranta', 'Pisco artesanal de tradición familiar', 89.90, '750', 1, 1),
('Vino Borgoña Reserva', 'Vino', 'Borgoña', 'Vino tinto de cuerpo medio', 65.00, '750', 1, 1);
```

## 🚀 Optimización y Performance

### Optimizaciones Implementadas
- **Lazy loading** para imágenes
- **Debounce/throttle** para eventos de scroll
- **CSS Grid/Flexbox** para layouts eficientes
- **Minificación** de CSS/JS (recomendado en producción)
- **Compresión** de imágenes
- **Preload** de recursos críticos

### Métricas de Performance
- **First Contentful Paint**: < 2s
- **Largest Contentful Paint**: < 3s
- **Cumulative Layout Shift**: < 0.1
- **Time to Interactive**: < 4s

## 🔒 Seguridad

### Medidas Implementadas
- Validación de entrada en formularios
- Escape de datos en salida HTML
- Prepared statements para consultas SQL
- Sanitización de parámetros URL

## 📞 Soporte

Para soporte técnico o consultas sobre el proyecto:
- **Email**: <EMAIL>
- **WhatsApp**: +51 924 352 679

## 📄 Licencia

Este proyecto está desarrollado específicamente para UVAMAYU - Reserva del Sol. Todos los derechos reservados.

---

**Desarrollado con ❤️ para UVAMAYU - Reserva del Sol**
*Tradición, Elegancia y Calidad en cada línea de código*
