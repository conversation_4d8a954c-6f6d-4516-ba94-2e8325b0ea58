<?php
// Configuración principal de UVAMAYU - Reserva del Sol

// Información de la marca
define('BRAND_NAME', 'UVAMAYU');
define('BRAND_SUBTITLE', 'Reserva del Sol');
define('BRAND_DESCRIPTION', 'Una marca peruana que nace de la tradición familiar y la pasión por la tierra. Elaboramos piscos y vinos artesanales con uvas cuidadosamente seleccionadas del valle.');

// Paleta de colores
define('COLOR_PRIMARY', '#84142A');        // Vino tinto / Borgoña
define('COLOR_SECONDARY', '#C49A52');      // Oro viejo / Dorado
define('COLOR_BLACK', '#000000');          // Negro profundo
define('COLOR_WHITE', '#FFFFFF');          // Blanco puro
define('COLOR_LIGHT_GRAY', '#F5F5F5');     // Gris claro

// Información de contacto
define('CONTACT_EMAIL', '<EMAIL>');
define('CONTACT_WHATSAPP', '+51 924 352 679');
define('CONTACT_WEBSITE', 'www.uvamayu.com');

// Redes sociales
define('SOCIAL_INSTAGRAM', '@uvamayu');
define('SOCIAL_FACEBOOK', '@uvamayu');
define('SOCIAL_TIKTOK', '@uvamayu');

// URLs de redes sociales
define('URL_INSTAGRAM', 'https://instagram.com/uvamayu');
define('URL_FACEBOOK', 'https://facebook.com/uvamayu');
define('URL_TIKTOK', 'https://tiktok.com/@uvamayu');

// Misión y Visión
define('MISSION', 'Producir y ofrecer piscos y vinos artesanales de alta calidad, rescatando lo mejor de la tradición peruana y proyectándola con identidad, elegancia y autenticidad al mundo.');
define('VISION', 'Ser una marca referente del pisco y vino artesanal peruano a nivel nacional e internacional, reconocida por su sabor único, presentación sofisticada y compromiso con la cultura y sostenibilidad.');

// Atributos destacados
$brand_attributes = [
    'Hecho en Perú',
    'Producción artesanal y limitada',
    'Calidad de exportación',
    'Presentación sofisticada',
    'Sabor auténtico y balanceado',
    'Ideal para regalos y ocasiones especiales'
];

// Categorías de productos
$product_categories = [
    'Pisco' => [
        'tipos' => ['Quebranta', 'Italia', 'Acholado'],
        'description' => 'Piscos artesanales de tradición peruana'
    ],
    'Vino' => [
        'tipos' => ['Borgoña', 'Mistela', 'Naranja', 'Ciruela', 'Puro Amor'],
        'description' => 'Vinos artesanales con sabores únicos'
    ]
];

// Tamaños disponibles
$product_sizes = [
    '100' => '100ml',
    '750' => '750ml',
    '3750' => '3.75L'
];

// Configuración de base de datos
define('DB_HOST', 'localhost');
define('DB_NAME', 'uvamayu_db');
define('DB_USER', 'root');
define('DB_PASS', '');

// Función para conectar a la base de datos
function getDBConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch(PDOException $e) {
        die("Error de conexión: " . $e->getMessage());
    }
}

// Función para obtener productos
function getProducts($category = null, $featured = null, $limit = null) {
    $pdo = getDBConnection();
    
    $sql = "SELECT * FROM productos WHERE activo = 1";
    $params = [];
    
    if ($category) {
        $sql .= " AND categoria = ?";
        $params[] = $category;
    }
    
    if ($featured !== null) {
        $sql .= " AND destacado = ?";
        $params[] = $featured;
    }
    
    $sql .= " ORDER BY destacado DESC, nombre ASC";
    
    if ($limit) {
        $sql .= " LIMIT ?";
        $params[] = $limit;
    }
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Función para obtener un producto por ID
function getProductById($id) {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT * FROM productos WHERE id = ? AND activo = 1");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Función para formatear precio
function formatPrice($price) {
    return 'S/ ' . number_format($price, 2);
}

// Función para generar enlace de WhatsApp
function getWhatsAppLink($message = '') {
    $phone = str_replace(['+', ' '], '', CONTACT_WHATSAPP);
    $encodedMessage = urlencode($message);
    return "https://wa.me/{$phone}?text={$encodedMessage}";
}

// Función para generar enlace de email
function getEmailLink($subject = '', $body = '') {
    $encodedSubject = urlencode($subject);
    $encodedBody = urlencode($body);
    return "mailto:" . CONTACT_EMAIL . "?subject={$encodedSubject}&body={$encodedBody}";
}

// Rutas de imágenes
define('ASSETS_PATH', 'assets/');
define('BANNERS_PATH', ASSETS_PATH . 'banners/');
define('AMBIENTE_PATH', ASSETS_PATH . 'ambiente/');
define('HISTORIA_PATH', ASSETS_PATH . 'historia/');
define('SECCIONES_PATH', ASSETS_PATH . 'secciones/');

// Configuración del sitio
define('SITE_TITLE', BRAND_NAME . ' - ' . BRAND_SUBTITLE);
define('SITE_DESCRIPTION', 'Piscos y vinos artesanales peruanos de alta calidad. Tradición familiar, elegancia y autenticidad en cada botella.');
define('SITE_KEYWORDS', 'pisco, vino, artesanal, peruano, tradición, calidad, elegancia, UVAMAYU');

?>
