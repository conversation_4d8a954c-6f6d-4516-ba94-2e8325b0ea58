/* UVAMAYU - Components Styles */

/* Footer Styles */
.footer {
    background: var(--gradient-dark);
    color: var(--color-white);
    margin-top: auto;
}

.footer-main {
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 3rem;
}

.footer-section {
    display: flex;
    flex-direction: column;
}

.brand-section {
    max-width: 350px;
}

.footer-logo .logo-text {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
}

.footer-logo .brand-name {
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-white);
    letter-spacing: 0.05em;
    line-height: 1;
}

.footer-logo .brand-subtitle {
    font-family: var(--font-accent);
    font-size: 1rem;
    color: var(--color-secondary);
    font-style: italic;
    letter-spacing: 0.1em;
    margin-top: -0.2rem;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: var(--color-white);
    text-decoration: none;
    transition: all var(--transition-medium);
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.social-link.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
}

.social-link.facebook:hover {
    background: #1877f2;
}

.social-link.tiktok:hover {
    background: #000000;
}

.footer-title {
    color: var(--color-secondary);
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-links a:hover {
    color: var(--color-secondary);
    padding-left: 0.5rem;
}

.footer-links a::before {
    content: '→';
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.footer-links a:hover::before {
    opacity: 1;
}

/* Contact Section */
.contact-info {
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-item i {
    color: var(--color-secondary);
    font-size: 1.2rem;
    margin-top: 0.2rem;
    width: 20px;
}

.contact-details {
    display: flex;
    flex-direction: column;
}

.contact-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 0.25rem;
}

.contact-value {
    color: var(--color-white);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.contact-value:hover {
    color: var(--color-secondary);
}

.contact-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Newsletter Section */
.footer-newsletter {
    background: rgba(0, 0, 0, 0.3);
    padding: 2rem 0;
    border-top: 1px solid rgba(196, 154, 82, 0.2);
    border-bottom: 1px solid rgba(196, 154, 82, 0.2);
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    align-items: center;
}

.newsletter-info h3 {
    color: var(--color-secondary);
    margin-bottom: 0.5rem;
}

.newsletter-info p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.newsletter-form .form-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.form-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: var(--color-white);
    font-size: 1rem;
    transition: all var(--transition-medium);
    backdrop-filter: blur(10px);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-input:focus {
    outline: none;
    border-color: var(--color-secondary);
    background: rgba(255, 255, 255, 0.15);
}

.form-note {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Footer Bottom */
.footer-bottom {
    padding: 1.5rem 0;
    background: rgba(0, 0, 0, 0.5);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.copyright {
    color: rgba(255, 255, 255, 0.7);
}

.copyright p {
    margin: 0;
    font-size: 0.9rem;
}

.made-in-peru {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.made-in-peru i {
    color: #e74c3c;
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.footer-legal {
    display: flex;
    gap: 2rem;
}

.footer-legal a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color var(--transition-fast);
}

.footer-legal a:hover {
    color: var(--color-secondary);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: var(--color-white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-heavy);
    transition: all var(--transition-medium);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(132, 20, 42, 0.4);
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 60px;
    height: 60px;
    background: #25D366;
    color: var(--color-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    text-decoration: none;
    box-shadow: var(--shadow-heavy);
    transition: all var(--transition-medium);
    z-index: 1000;
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
    animation: none;
}

/* Image Utilities */
.img-fluid {
    max-width: 100%;
    height: auto;
}

.img-cover {
    object-fit: cover;
}

.img-contain {
    object-fit: contain;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--color-light-gray);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 600;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 400px;
    box-shadow: var(--shadow-heavy);
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: linear-gradient(135deg, #10B981, #059669);
}

.notification-error {
    background: linear-gradient(135deg, #EF4444, #DC2626);
}

.notification-warning {
    background: linear-gradient(135deg, #F59E0B, #D97706);
}

.notification-info {
    background: linear-gradient(135deg, #3B82F6, #2563EB);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-medium);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--color-white);
    border-radius: 1rem;
    padding: 2rem;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    position: relative;
    transform: scale(0.8);
    transition: transform var(--transition-medium);
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 40px;
    height: 40px;
    border: none;
    background: var(--color-light-gray);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--color-dark-gray);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--color-primary);
    color: var(--color-white);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
    
    .newsletter-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .brand-section {
        max-width: none;
    }
    
    .newsletter-form .form-group {
        flex-direction: column;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .footer-legal {
        justify-content: center;
        gap: 1rem;
    }
    
    .contact-actions {
        justify-content: center;
    }
    
    .whatsapp-float {
        bottom: 6rem;
        left: 1rem;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .back-to-top {
        bottom: 6rem;
        right: 1rem;
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .footer-main {
        padding: 3rem 0 1.5rem;
    }
    
    .footer-newsletter {
        padding: 1.5rem 0;
    }
    
    .footer-bottom {
        padding: 1rem 0;
    }
    
    .social-links {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .social-link {
        justify-content: center;
    }
    
    .footer-legal {
        flex-direction: column;
        gap: 0.5rem;
    }
}
