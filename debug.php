<?php
// Archivo de diagnóstico para identificar problemas
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Diagnóstico UVAMAYU</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
.info { color: blue; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

// 1. Verificar PHP
echo "<div class='section'>";
echo "<h2>📋 Información de PHP</h2>";
echo "<p class='info'>Versión de PHP: " . phpversion() . "</p>";
echo "<p class='info'>Memoria límite: " . ini_get('memory_limit') . "</p>";
echo "<p class='info'>Tiempo máximo de ejecución: " . ini_get('max_execution_time') . " segundos</p>";
echo "</div>";

// 2. Verificar extensiones PHP necesarias
echo "<div class='section'>";
echo "<h2>🔧 Extensiones PHP</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'mysqli'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p class='success'>✅ $ext: Instalada</p>";
    } else {
        echo "<p class='error'>❌ $ext: NO instalada</p>";
    }
}
echo "</div>";

// 3. Verificar archivos del proyecto
echo "<div class='section'>";
echo "<h2>📁 Archivos del Proyecto</h2>";
$required_files = [
    'config.php',
    'index.php',
    'productos.php',
    'producto.php',
    'includes/header.php',
    'includes/footer.php',
    'css/style.css',
    'js/main.js'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $file: Existe</p>";
    } else {
        echo "<p class='error'>❌ $file: NO existe</p>";
    }
}
echo "</div>";

// 4. Probar configuración (sin incluir config.php aún)
echo "<div class='section'>";
echo "<h2>⚙️ Configuración de Base de Datos</h2>";

// Definir configuración directamente para prueba
$db_host = 'localhost';
$db_name = 'uvamayu';
$db_user = 'root';
$db_pass = 'Mumma24$';

echo "<p class='info'>Host: $db_host</p>";
echo "<p class='info'>Base de datos: $db_name</p>";
echo "<p class='info'>Usuario: $db_user</p>";
echo "<p class='info'>Contraseña: " . (empty($db_pass) ? 'Vacía' : 'Configurada') . "</p>";
echo "</div>";

// 5. Probar conexión a MySQL
echo "<div class='section'>";
echo "<h2>🔌 Prueba de Conexión</h2>";

try {
    // Primero probar conexión sin especificar base de datos
    echo "<p class='info'>Probando conexión al servidor MySQL...</p>";
    $pdo_server = new PDO("mysql:host=$db_host;charset=utf8", $db_user, $db_pass);
    $pdo_server->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Conexión al servidor MySQL: EXITOSA</p>";
    
    // Verificar si la base de datos existe
    echo "<p class='info'>Verificando si existe la base de datos '$db_name'...</p>";
    $stmt = $pdo_server->query("SHOW DATABASES LIKE '$db_name'");
    $db_exists = $stmt->rowCount() > 0;
    
    if ($db_exists) {
        echo "<p class='success'>✅ Base de datos '$db_name': EXISTE</p>";
        
        // Probar conexión a la base de datos específica
        echo "<p class='info'>Probando conexión a la base de datos...</p>";
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p class='success'>✅ Conexión a la base de datos: EXITOSA</p>";
        
        // Verificar tablas
        echo "<p class='info'>Verificando tabla 'productos'...</p>";
        $stmt = $pdo->query("SHOW TABLES LIKE 'productos'");
        $table_exists = $stmt->rowCount() > 0;
        
        if ($table_exists) {
            echo "<p class='success'>✅ Tabla 'productos': EXISTE</p>";
            
            // Contar productos
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM productos");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            echo "<p class='info'>📊 Productos en la base de datos: $count</p>";
            
            if ($count == 0) {
                echo "<p class='warning'>⚠️ La tabla productos está vacía. Esto puede causar que las páginas no muestren contenido.</p>";
            }
        } else {
            echo "<p class='error'>❌ Tabla 'productos': NO EXISTE</p>";
            echo "<p class='warning'>⚠️ Necesitas crear la tabla productos. Ver instrucciones abajo.</p>";
        }
        
    } else {
        echo "<p class='error'>❌ Base de datos '$db_name': NO EXISTE</p>";
        echo "<p class='warning'>⚠️ Necesitas crear la base de datos. Ver instrucciones abajo.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p class='error'>❌ Error de conexión: " . $e->getMessage() . "</p>";
    
    // Sugerencias basadas en el error
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "<p class='warning'>💡 Sugerencia: Verifica el usuario y contraseña de MySQL</p>";
    } elseif (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo "<p class='warning'>💡 Sugerencia: Verifica que MySQL esté ejecutándose</p>";
    } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "<p class='warning'>💡 Sugerencia: La base de datos no existe, necesitas crearla</p>";
    }
}
echo "</div>";

// 6. Probar carga de config.php
echo "<div class='section'>";
echo "<h2>📄 Prueba de config.php</h2>";
try {
    echo "<p class='info'>Intentando cargar config.php...</p>";
    ob_start();
    include 'config.php';
    $config_output = ob_get_clean();
    
    if (empty($config_output)) {
        echo "<p class='success'>✅ config.php se carga sin errores visibles</p>";
        
        // Verificar constantes definidas
        $constants = ['BRAND_NAME', 'COLOR_PRIMARY', 'DB_HOST', 'DB_NAME', 'DB_USER'];
        foreach ($constants as $const) {
            if (defined($const)) {
                echo "<p class='success'>✅ Constante $const: Definida</p>";
            } else {
                echo "<p class='error'>❌ Constante $const: NO definida</p>";
            }
        }
        
        // Probar función de conexión
        if (function_exists('getDBConnection')) {
            echo "<p class='success'>✅ Función getDBConnection: Definida</p>";
            try {
                $test_connection = getDBConnection();
                echo "<p class='success'>✅ getDBConnection(): Funciona correctamente</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ getDBConnection(): Error - " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p class='error'>❌ Función getDBConnection: NO definida</p>";
        }
        
    } else {
        echo "<p class='error'>❌ config.php produce salida inesperada:</p>";
        echo "<pre>" . htmlspecialchars($config_output) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error al cargar config.php: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 7. Instrucciones de solución
echo "<div class='section'>";
echo "<h2>🛠️ Instrucciones de Solución</h2>";

echo "<h3>Si la base de datos no existe:</h3>";
echo "<pre>
1. Abre phpMyAdmin o tu cliente MySQL
2. Ejecuta este comando:
   CREATE DATABASE uvamayu CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
</pre>";

echo "<h3>Si la tabla productos no existe:</h3>";
echo "<pre>
USE uvamayu;

CREATE TABLE IF NOT EXISTS productos (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nombre VARCHAR(100) NOT NULL,
  categoria ENUM('Pisco','Vino') NOT NULL,
  tipo VARCHAR(50) NOT NULL,
  descripcion TEXT,
  varietal VARCHAR(100),
  aroma VARCHAR(100),
  maridaje VARCHAR(100),
  graduacion VARCHAR(20),
  tamanio ENUM('100','750','3750') NOT NULL,
  precio_unitario DECIMAL(8,2) NOT NULL,
  precio_caja DECIMAL(8,2),
  imagen_principal VARCHAR(255),
  stock INT DEFAULT 0,
  destacado BOOL DEFAULT 0,
  activo BOOL DEFAULT 1
);
</pre>";

echo "<h3>Datos de prueba (opcional):</h3>";
echo "<pre>
INSERT INTO productos (nombre, categoria, tipo, descripcion, precio_unitario, tamanio, destacado, activo) VALUES
('Pisco Quebranta Premium', 'Pisco', 'Quebranta', 'Pisco artesanal de tradición familiar', 89.90, '750', 1, 1),
('Vino Borgoña Reserva', 'Vino', 'Borgoña', 'Vino tinto de cuerpo medio', 65.00, '750', 1, 1),
('Pisco Italia Especial', 'Pisco', 'Italia', 'Pisco aromático de uva Italia', 95.00, '750', 0, 1);
</pre>";

echo "</div>";

// 8. Verificar permisos de archivos
echo "<div class='section'>";
echo "<h2>🔐 Permisos de Archivos</h2>";
$check_files = ['config.php', 'index.php', 'css', 'js', 'assets'];
foreach ($check_files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $perms_octal = substr(sprintf('%o', $perms), -4);
        echo "<p class='info'>$file: Permisos $perms_octal</p>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎯 Próximos Pasos</h2>";
echo "<ol>";
echo "<li>Si hay errores de conexión, corrige la configuración de MySQL</li>";
echo "<li>Si falta la base de datos o tabla, créalas usando los comandos SQL de arriba</li>";
echo "<li>Si todo está bien aquí, el problema puede estar en index.php</li>";
echo "<li>Después de corregir, prueba acceder a <a href='index.php'>index.php</a></li>";
echo "</ol>";
echo "</div>";

echo "<p><strong>Fecha del diagnóstico:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
