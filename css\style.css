/* UVAMAYU - Reserva del Sol - Estilos principales */

/* Variables CSS para colores */
:root {
    --color-primary: #84142A;
    --color-secondary: #C49A52;
    --color-black: #000000;
    --color-white: #FFFFFF;
    --color-light-gray: #F5F5F5;
    --color-dark-gray: #333333;
    --color-medium-gray: #666666;
    
    /* Gradientes */
    --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, #a01b35 100%);
    --gradient-secondary: linear-gradient(135deg, var(--color-secondary) 0%, #d4a862 100%);
    --gradient-dark: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
    
    /* Sombras */
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.25);
    --shadow-primary: 0 4px 20px rgba(132, 20, 42, 0.3);
    
    /* Transiciones */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Tipografía */
    --font-primary: 'Playfair Display', serif;
    --font-secondary: 'Inter', sans-serif;
    --font-accent: 'Cormorant Garamond', serif;
}

/* Reset y base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--color-dark-gray);
    background-color: var(--color-white);
    overflow-x: hidden;
}

/* Tipografía */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 700;
    line-height: 1.2;
    color: var(--color-primary);
    margin-bottom: 1rem;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    letter-spacing: -0.02em;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    letter-spacing: -0.01em;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2.25rem);
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.75rem);
}

p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    line-height: 1.7;
}

.text-accent {
    font-family: var(--font-accent);
    font-style: italic;
    color: var(--color-secondary);
}

.text-primary {
    color: var(--color-primary);
}

.text-secondary {
    color: var(--color-secondary);
}

.text-center {
    text-align: center;
}

.text-large {
    font-size: 1.25rem;
}

/* Contenedores */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.container-fluid {
    width: 100%;
    padding: 0 2rem;
}

.section {
    padding: 5rem 0;
}

.section-dark {
    background: var(--gradient-dark);
    color: var(--color-white);
}

.section-light {
    background-color: var(--color-light-gray);
}

/* Botones */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.5rem;
    font-family: var(--font-secondary);
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
    z-index: -1;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--color-white);
    box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(132, 20, 42, 0.4);
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: var(--color-white);
    box-shadow: var(--shadow-medium);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(196, 154, 82, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--color-primary);
    border: 2px solid var(--color-primary);
}

.btn-outline:hover {
    background: var(--color-primary);
    color: var(--color-white);
    transform: translateY(-2px);
}

.btn-ghost {
    background: transparent;
    color: var(--color-white);
    border: 2px solid var(--color-white);
}

.btn-ghost:hover {
    background: var(--color-white);
    color: var(--color-primary);
}

/* Cards */
.card {
    background: var(--color-white);
    border-radius: 1rem;
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: all var(--transition-medium);
    position: relative;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.card-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.card:hover .card-image {
    transform: scale(1.05);
}

.card-content {
    padding: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--color-primary);
}

.card-text {
    color: var(--color-medium-gray);
    margin-bottom: 1rem;
}

/* Grid system */
.grid {
    display: grid;
    gap: 2rem;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Utilidades */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all var(--transition-slow);
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.overlay {
    position: relative;
}

.overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.overlay > * {
    position: relative;
    z-index: 2;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .section {
        padding: 3rem 0;
    }
    
    .grid {
        gap: 1rem;
    }
    
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    html {
        font-size: 14px;
    }
    
    .container {
        padding: 0 0.5rem;
    }
    
    .section {
        padding: 2rem 0;
    }
}

/* Animaciones */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease forwards;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s ease forwards;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease forwards;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-dark);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity var(--transition-slow);
}

.preloader.hidden {
    opacity: 0;
    pointer-events: none;
}

.loader {
    width: 50px;
    height: 50px;
    border: 3px solid var(--color-secondary);
    border-top: 3px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Advanced Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes skeleton-loading {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-180deg) scale(0.5);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes flipIn {
    from {
        opacity: 0;
        transform: perspective(400px) rotateY(90deg);
    }
    to {
        opacity: 1;
        transform: perspective(400px) rotateY(0deg);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--color-secondary);
    }
    50% {
        box-shadow: 0 0 20px var(--color-secondary), 0 0 30px var(--color-secondary);
    }
}

@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

/* Text Reveal Animation */
.word-reveal {
    display: inline-block;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.word-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Button Animations */
.btn {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-medium);
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::after {
    width: 300px;
    height: 300px;
}

/* Magnetic Effect */
.btn, .nav-link, .action-btn {
    transition: transform var(--transition-medium);
}

/* Scroll-based Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

.animate-slide-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.8s ease;
}

.animate-slide-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.animate-slide-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.8s ease;
}

.animate-slide-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.animate-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.8s ease;
}

.animate-scale.visible {
    opacity: 1;
    transform: scale(1);
}

.animate-rotate {
    opacity: 0;
    transform: rotate(-10deg);
    transition: all 0.8s ease;
}

.animate-rotate.visible {
    opacity: 1;
    transform: rotate(0deg);
}

/* Stagger Animation */
.stagger-animation > * {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.stagger-animation.visible > *:nth-child(1) { transition-delay: 0.1s; }
.stagger-animation.visible > *:nth-child(2) { transition-delay: 0.2s; }
.stagger-animation.visible > *:nth-child(3) { transition-delay: 0.3s; }
.stagger-animation.visible > *:nth-child(4) { transition-delay: 0.4s; }
.stagger-animation.visible > *:nth-child(5) { transition-delay: 0.5s; }
.stagger-animation.visible > *:nth-child(6) { transition-delay: 0.6s; }

.stagger-animation.visible > * {
    opacity: 1;
    transform: translateY(0);
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.loading-pulse {
    animation: pulse 1.5s infinite;
}

.loading-bounce {
    animation: bounceIn 1s ease;
}

/* Hover Effects */
.hover-lift {
    transition: transform var(--transition-medium);
}

.hover-lift:hover {
    transform: translateY(-5px);
}

.hover-glow {
    transition: all var(--transition-medium);
}

.hover-glow:hover {
    animation: glow 1s ease-in-out;
}

.hover-scale {
    transition: transform var(--transition-medium);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform var(--transition-medium);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Entrance Animations */
.entrance-fade {
    animation: fadeInUp 0.8s ease forwards;
}

.entrance-slide-left {
    animation: slideInFromLeft 0.8s ease forwards;
}

.entrance-slide-right {
    animation: slideInFromRight 0.8s ease forwards;
}

.entrance-slide-up {
    animation: slideUp 0.8s ease forwards;
}

.entrance-slide-down {
    animation: slideDown 0.8s ease forwards;
}

.entrance-scale {
    animation: scaleIn 0.8s ease forwards;
}

.entrance-rotate {
    animation: rotateIn 0.8s ease forwards;
}

.entrance-bounce {
    animation: bounceIn 0.8s ease forwards;
}

.entrance-zoom {
    animation: zoomIn 0.8s ease forwards;
}

.entrance-flip {
    animation: flipIn 0.8s ease forwards;
}

/* Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .parallax {
        background-attachment: scroll;
    }
}

/* High Contrast Support */
@media (prefers-contrast: high) {
    :root {
        --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.3);
        --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.4);
        --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.5);
    }

    .btn {
        border: 2px solid currentColor;
    }
}
