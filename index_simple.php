<?php
// Versión simplificada de index.php para debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔍 Iniciando carga de index.php...<br>";

try {
    echo "📄 Cargando config.php...<br>";
    include 'config.php';
    echo "✅ config.php cargado correctamente<br>";
    
    echo "🗄️ Probando obtener productos...<br>";
    $featured_products = getProducts(null, 1, 6);
    echo "✅ Productos obtenidos: " . count($featured_products) . "<br>";
    
    echo "📋 Configurando variables de página...<br>";
    $page_title = "Inicio";
    $page_description = "UVAMAYU - Reserva del Sol. Piscos y vinos artesanales peruanos de alta calidad.";
    $additional_css = ['css/home.css'];
    echo "✅ Variables configuradas<br>";
    
    echo "🎨 Cargando header...<br>";
    include 'includes/header.php';
    echo "✅ Header cargado<br>";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "<br>";
    echo "📍 Archivo: " . $e->getFile() . "<br>";
    echo "📍 Línea: " . $e->getLine() . "<br>";
    die();
}
?>

<!-- Contenido HTML simplificado -->
<div style="padding: 100px 20px; text-align: center; font-family: Arial, sans-serif;">
    <h1 style="color: #84142A;">🍷 UVAMAYU - Reserva del Sol</h1>
    <p style="font-size: 1.2em; color: #666;">Sitio web cargado correctamente</p>
    
    <div style="margin: 40px 0;">
        <h2>Productos Destacados (<?php echo count($featured_products); ?>)</h2>
        <?php if (!empty($featured_products)): ?>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                <?php foreach (array_slice($featured_products, 0, 3) as $producto): ?>
                    <div style="border: 1px solid #ddd; padding: 20px; border-radius: 8px;">
                        <h3 style="color: #84142A;"><?php echo htmlspecialchars($producto['nombre']); ?></h3>
                        <p><strong>Categoría:</strong> <?php echo $producto['categoria']; ?></p>
                        <p><strong>Precio:</strong> <?php echo formatPrice($producto['precio_unitario']); ?></p>
                        <?php if ($producto['destacado']): ?>
                            <span style="background: #C49A52; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em;">⭐ Destacado</span>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <p style="color: #999;">No hay productos destacados disponibles</p>
        <?php endif; ?>
    </div>
    
    <div style="margin: 40px 0;">
        <h2>Enlaces de Navegación</h2>
        <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
            <a href="productos.php" style="background: #84142A; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Ver Productos</a>
            <a href="test.php" style="background: #C49A52; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Página de Pruebas</a>
            <a href="debug.php" style="background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Diagnóstico</a>
        </div>
    </div>
    
    <div style="margin: 40px 0; padding: 20px; background: #f5f5f5; border-radius: 8px;">
        <h3>Información del Sistema</h3>
        <p><strong>PHP:</strong> <?php echo phpversion(); ?></p>
        <p><strong>Marca:</strong> <?php echo BRAND_NAME; ?></p>
        <p><strong>Email:</strong> <?php echo CONTACT_EMAIL; ?></p>
        <p><strong>WhatsApp:</strong> <?php echo CONTACT_WHATSAPP; ?></p>
        <p><strong>Fecha:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    </div>
</div>

<?php
try {
    echo "🦶 Cargando footer...<br>";
    include 'includes/footer.php';
    echo "✅ Footer cargado<br>";
} catch (Exception $e) {
    echo "❌ ERROR en footer: " . $e->getMessage() . "<br>";
}
?>

<script>
console.log('🎉 JavaScript cargado correctamente');
console.log('📊 Productos destacados:', <?php echo count($featured_products); ?>);
</script>
