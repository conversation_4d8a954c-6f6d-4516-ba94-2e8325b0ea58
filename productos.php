<?php
$page_title = "Productos";
$page_description = "Descubre nuestra colección completa de piscos y vinos artesanales UVAMAYU. Calidad premium, tradición peruana y sabores únicos.";
$additional_css = ['css/productos.css'];
$additional_js = ['js/productos.js'];

include 'includes/header.php';

// Obtener parámetros de filtro
$categoria_filter = isset($_GET['categoria']) ? $_GET['categoria'] : null;
$destacado_filter = isset($_GET['destacado']) ? (bool)$_GET['destacado'] : null;
$tipo_filter = isset($_GET['tipo']) ? $_GET['tipo'] : null;
$search_query = isset($_GET['search']) ? trim($_GET['search']) : null;

// Obtener productos según filtros
$productos = getProducts($categoria_filter, $destacado_filter);

// Filtrar por tipo si se especifica
if ($tipo_filter && !empty($productos)) {
    $productos = array_filter($productos, function($producto) use ($tipo_filter) {
        return stripos($producto['tipo'], $tipo_filter) !== false;
    });
}

// Filtrar por búsqueda si se especifica
if ($search_query && !empty($productos)) {
    $productos = array_filter($productos, function($producto) use ($search_query) {
        return stripos($producto['nombre'], $search_query) !== false ||
               stripos($producto['tipo'], $search_query) !== false ||
               stripos($producto['descripcion'], $search_query) !== false ||
               stripos($producto['varietal'], $search_query) !== false;
    });
}

// Obtener tipos únicos para filtros
$tipos_pisco = [];
$tipos_vino = [];
foreach (getProducts() as $producto) {
    if ($producto['categoria'] === 'Pisco' && !in_array($producto['tipo'], $tipos_pisco)) {
        $tipos_pisco[] = $producto['tipo'];
    } elseif ($producto['categoria'] === 'Vino' && !in_array($producto['tipo'], $tipos_vino)) {
        $tipos_vino[] = $producto['tipo'];
    }
}
?>

<!-- Hero Section -->
<section class="page-hero">
    <div class="hero-background" style="background-image: url('<?php echo BANNERS_PATH; ?>banner4.png');"></div>
    <div class="hero-overlay"></div>
    <div class="container">
        <div class="hero-content text-center fade-in">
            <h1>Nuestros <span class="text-secondary">Productos</span></h1>
            <p class="hero-subtitle">
                Descubre la excelencia en cada botella. Piscos y vinos artesanales 
                que representan lo mejor de la tradición peruana.
            </p>
            
            <!-- Breadcrumb -->
            <nav class="breadcrumb">
                <a href="index.php">Inicio</a>
                <span class="separator">/</span>
                <span class="current">Productos</span>
                <?php if ($categoria_filter): ?>
                    <span class="separator">/</span>
                    <span class="current"><?php echo $categoria_filter; ?></span>
                <?php endif; ?>
            </nav>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="filters-section">
    <div class="container">
        <div class="filters-wrapper">
            <!-- Search Bar -->
            <div class="search-container">
                <form class="search-form" method="GET">
                    <div class="search-input-group">
                        <input type="text" 
                               name="search" 
                               placeholder="Buscar productos..." 
                               value="<?php echo htmlspecialchars($search_query ?: ''); ?>"
                               class="search-input">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    
                    <!-- Mantener otros filtros en la búsqueda -->
                    <?php if ($categoria_filter): ?>
                        <input type="hidden" name="categoria" value="<?php echo $categoria_filter; ?>">
                    <?php endif; ?>
                    <?php if ($destacado_filter): ?>
                        <input type="hidden" name="destacado" value="1">
                    <?php endif; ?>
                    <?php if ($tipo_filter): ?>
                        <input type="hidden" name="tipo" value="<?php echo $tipo_filter; ?>">
                    <?php endif; ?>
                </form>
            </div>
            
            <!-- Filter Buttons -->
            <div class="filter-buttons">
                <button class="filter-btn <?php echo !$categoria_filter ? 'active' : ''; ?>" 
                        data-filter="all">
                    <i class="fas fa-th-large"></i>
                    Todos
                </button>
                
                <button class="filter-btn <?php echo $categoria_filter === 'Pisco' ? 'active' : ''; ?>" 
                        data-filter="pisco">
                    <i class="fas fa-wine-bottle"></i>
                    Piscos
                </button>
                
                <button class="filter-btn <?php echo $categoria_filter === 'Vino' ? 'active' : ''; ?>" 
                        data-filter="vino">
                    <i class="fas fa-wine-glass"></i>
                    Vinos
                </button>
                
                <button class="filter-btn <?php echo $destacado_filter ? 'active' : ''; ?>" 
                        data-filter="destacados">
                    <i class="fas fa-star"></i>
                    Destacados
                </button>
            </div>
            
            <!-- Advanced Filters -->
            <div class="advanced-filters">
                <div class="filter-dropdown">
                    <button class="dropdown-toggle">
                        <i class="fas fa-filter"></i>
                        Tipo de Producto
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu">
                        <div class="dropdown-section">
                            <h4>Piscos</h4>
                            <?php foreach ($tipos_pisco as $tipo): ?>
                                <a href="?categoria=Pisco&tipo=<?php echo urlencode($tipo); ?>" 
                                   class="dropdown-item <?php echo $tipo_filter === $tipo ? 'active' : ''; ?>">
                                    <?php echo htmlspecialchars($tipo); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                        <div class="dropdown-section">
                            <h4>Vinos</h4>
                            <?php foreach ($tipos_vino as $tipo): ?>
                                <a href="?categoria=Vino&tipo=<?php echo urlencode($tipo); ?>" 
                                   class="dropdown-item <?php echo $tipo_filter === $tipo ? 'active' : ''; ?>">
                                    <?php echo htmlspecialchars($tipo); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <div class="filter-sort">
                    <select class="sort-select" id="sortProducts">
                        <option value="name">Ordenar por Nombre</option>
                        <option value="price-low">Precio: Menor a Mayor</option>
                        <option value="price-high">Precio: Mayor a Menor</option>
                        <option value="featured">Destacados Primero</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Products Section -->
<section class="products-section section">
    <div class="container">
        <!-- Results Info -->
        <div class="results-info fade-in">
            <div class="results-count">
                <span class="count"><?php echo count($productos); ?></span> 
                producto<?php echo count($productos) !== 1 ? 's' : ''; ?> encontrado<?php echo count($productos) !== 1 ? 's' : ''; ?>
                <?php if ($categoria_filter || $destacado_filter || $tipo_filter || $search_query): ?>
                    <div class="active-filters">
                        <?php if ($categoria_filter): ?>
                            <span class="filter-tag">
                                <?php echo $categoria_filter; ?>
                                <a href="?<?php echo http_build_query(array_diff_key($_GET, ['categoria' => ''])); ?>">×</a>
                            </span>
                        <?php endif; ?>
                        <?php if ($destacado_filter): ?>
                            <span class="filter-tag">
                                Destacados
                                <a href="?<?php echo http_build_query(array_diff_key($_GET, ['destacado' => ''])); ?>">×</a>
                            </span>
                        <?php endif; ?>
                        <?php if ($tipo_filter): ?>
                            <span class="filter-tag">
                                <?php echo $tipo_filter; ?>
                                <a href="?<?php echo http_build_query(array_diff_key($_GET, ['tipo' => ''])); ?>">×</a>
                            </span>
                        <?php endif; ?>
                        <?php if ($search_query): ?>
                            <span class="filter-tag">
                                "<?php echo htmlspecialchars($search_query); ?>"
                                <a href="?<?php echo http_build_query(array_diff_key($_GET, ['search' => ''])); ?>">×</a>
                            </span>
                        <?php endif; ?>
                        <a href="productos.php" class="clear-filters">Limpiar filtros</a>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="view-toggle">
                <button class="view-btn active" data-view="grid">
                    <i class="fas fa-th"></i>
                </button>
                <button class="view-btn" data-view="list">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="products-container">
            <?php if (!empty($productos)): ?>
                <div class="products-grid grid grid-3" id="productsGrid">
                    <?php foreach ($productos as $producto): ?>
                        <div class="product-card fade-in" 
                             data-category="<?php echo strtolower($producto['categoria']); ?>"
                             data-price="<?php echo $producto['precio_unitario']; ?>"
                             data-featured="<?php echo $producto['destacado'] ? '1' : '0'; ?>"
                             data-name="<?php echo htmlspecialchars($producto['nombre']); ?>">
                            
                            <div class="product-image-container">
                                <img src="<?php echo $producto['imagen_principal'] ?: 'assets/placeholder-product.jpg'; ?>" 
                                     alt="<?php echo htmlspecialchars($producto['nombre']); ?>" 
                                     class="product-image"
                                     loading="lazy">
                                
                                <div class="product-overlay">
                                    <div class="product-actions">
                                        <a href="producto.php?id=<?php echo $producto['id']; ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                            Ver Detalles
                                        </a>
                                        <button onclick="sendWhatsApp('Hola, me interesa el producto: <?php echo htmlspecialchars($producto['nombre']); ?> - <?php echo formatPrice($producto['precio_unitario']); ?>')" 
                                                class="btn btn-secondary btn-sm">
                                            <i class="fab fa-whatsapp"></i>
                                            Consultar
                                        </button>
                                        <button onclick="sendEmail('Consulta sobre <?php echo htmlspecialchars($producto['nombre']); ?>', 'Hola, me gustaría recibir más información sobre el producto: <?php echo htmlspecialchars($producto['nombre']); ?>')" 
                                                class="btn btn-outline btn-sm">
                                            <i class="fas fa-envelope"></i>
                                            Email
                                        </button>
                                    </div>
                                </div>
                                
                                <?php if ($producto['destacado']): ?>
                                    <div class="product-badge featured">
                                        <i class="fas fa-star"></i>
                                        Destacado
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($producto['stock'] <= 0): ?>
                                    <div class="product-badge out-of-stock">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Agotado
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-info">
                                <div class="product-category"><?php echo $producto['categoria']; ?></div>
                                <h3 class="product-title">
                                    <a href="producto.php?id=<?php echo $producto['id']; ?>">
                                        <?php echo htmlspecialchars($producto['nombre']); ?>
                                    </a>
                                </h3>
                                <p class="product-type"><?php echo htmlspecialchars($producto['tipo']); ?></p>
                                
                                <div class="product-details">
                                    <?php if ($producto['varietal']): ?>
                                        <div class="product-detail">
                                            <i class="fas fa-grape-cluster"></i>
                                            <span><?php echo htmlspecialchars($producto['varietal']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($producto['graduacion']): ?>
                                        <div class="product-detail">
                                            <i class="fas fa-percentage"></i>
                                            <span><?php echo htmlspecialchars($producto['graduacion']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="product-detail">
                                        <i class="fas fa-wine-bottle"></i>
                                        <span><?php echo $product_sizes[$producto['tamanio']]; ?></span>
                                    </div>
                                </div>
                                
                                <?php if ($producto['descripcion']): ?>
                                    <p class="product-description">
                                        <?php echo htmlspecialchars(substr($producto['descripcion'], 0, 100)); ?>
                                        <?php if (strlen($producto['descripcion']) > 100): ?>...<?php endif; ?>
                                    </p>
                                <?php endif; ?>
                                
                                <div class="product-pricing">
                                    <div class="price-main"><?php echo formatPrice($producto['precio_unitario']); ?></div>
                                    <?php if ($producto['precio_caja']): ?>
                                        <div class="price-bulk">Caja: <?php echo formatPrice($producto['precio_caja']); ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="product-stock">
                                    <?php if ($producto['stock'] > 0): ?>
                                        <span class="stock-available">
                                            <i class="fas fa-check-circle"></i>
                                            Disponible
                                        </span>
                                    <?php else: ?>
                                        <span class="stock-unavailable">
                                            <i class="fas fa-times-circle"></i>
                                            No disponible
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-products">
                    <div class="no-products-content">
                        <i class="fas fa-search"></i>
                        <h3>No se encontraron productos</h3>
                        <p>
                            <?php if ($search_query): ?>
                                No encontramos productos que coincidan con "<?php echo htmlspecialchars($search_query); ?>".
                            <?php else: ?>
                                No hay productos disponibles con los filtros seleccionados.
                            <?php endif; ?>
                        </p>
                        <div class="no-products-actions">
                            <a href="productos.php" class="btn btn-primary">
                                <i class="fas fa-th-large"></i>
                                Ver Todos los Productos
                            </a>
                            <a href="contacto.php" class="btn btn-outline">
                                <i class="fas fa-phone"></i>
                                Contactar
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section section-dark cta-section">
    <div class="container">
        <div class="cta-content text-center fade-in">
            <h2>¿No encuentras lo que buscas?</h2>
            <p class="text-large">
                Contáctanos directamente y te ayudaremos a encontrar el producto perfecto 
                para tu ocasión especial.
            </p>
            <div class="cta-actions">
                <a href="<?php echo getWhatsAppLink('Hola, necesito ayuda para encontrar un producto específico'); ?>" 
                   target="_blank" 
                   class="btn btn-primary btn-lg">
                    <i class="fab fa-whatsapp"></i>
                    Consultar por WhatsApp
                </a>
                <a href="contacto.php" class="btn btn-outline btn-lg">
                    <i class="fas fa-envelope"></i>
                    Enviar Consulta
                </a>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
