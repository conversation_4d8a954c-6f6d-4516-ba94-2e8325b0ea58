/* UVAMAYU - Products Page Styles */

/* Page Hero */
.page-hero {
    position: relative;
    height: 60vh;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    filter: brightness(0.7);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(132, 20, 42, 0.5) 100%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: var(--color-white);
    max-width: 800px;
    padding: 2rem;
}

.hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1rem;
    color: var(--color-white);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

/* Breadcrumb */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    margin-top: 1rem;
}

.breadcrumb a {
    color: var(--color-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb a:hover {
    color: var(--color-white);
}

.breadcrumb .separator {
    color: rgba(255, 255, 255, 0.6);
}

.breadcrumb .current {
    color: var(--color-white);
}

/* Filters Section */
.filters-section {
    background: var(--color-light-gray);
    padding: 2rem 0;
    border-bottom: 1px solid rgba(132, 20, 42, 0.1);
}

.filters-wrapper {
    display: grid;
    gap: 2rem;
}

/* Search Container */
.search-container {
    display: flex;
    justify-content: center;
}

.search-form {
    width: 100%;
    max-width: 500px;
}

.search-input-group {
    position: relative;
    display: flex;
}

.search-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid var(--color-primary);
    border-radius: 0.5rem 0 0 0.5rem;
    font-size: 1rem;
    background: var(--color-white);
    transition: all var(--transition-medium);
}

.search-input:focus {
    outline: none;
    border-color: var(--color-secondary);
    box-shadow: 0 0 0 3px rgba(196, 154, 82, 0.2);
}

.search-btn {
    padding: 1rem 1.5rem;
    background: var(--gradient-primary);
    color: var(--color-white);
    border: none;
    border-radius: 0 0.5rem 0.5rem 0;
    cursor: pointer;
    transition: all var(--transition-medium);
    font-size: 1rem;
}

.search-btn:hover {
    background: var(--gradient-secondary);
    transform: translateY(-1px);
}

/* Filter Buttons */
.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--color-white);
    color: var(--color-primary);
    border: 2px solid var(--color-primary);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-medium);
    font-weight: 600;
    text-decoration: none;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--gradient-primary);
    color: var(--color-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.filter-btn i {
    font-size: 1rem;
}

/* Advanced Filters */
.advanced-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.filter-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: var(--color-white);
    border: 1px solid var(--color-primary);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-medium);
    font-weight: 500;
    color: var(--color-primary);
}

.dropdown-toggle:hover {
    background: var(--color-light-gray);
}

.dropdown-toggle .fa-chevron-down {
    transition: transform var(--transition-fast);
}

.filter-dropdown.active .dropdown-toggle .fa-chevron-down {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 250px;
    background: var(--color-white);
    border: 1px solid var(--color-light-gray);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-heavy);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-medium);
    z-index: 100;
    max-height: 300px;
    overflow-y: auto;
}

.filter-dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-section {
    padding: 1rem;
    border-bottom: 1px solid var(--color-light-gray);
}

.dropdown-section:last-child {
    border-bottom: none;
}

.dropdown-section h4 {
    color: var(--color-primary);
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.dropdown-item {
    display: block;
    padding: 0.5rem 0.75rem;
    color: var(--color-dark-gray);
    text-decoration: none;
    border-radius: 0.25rem;
    transition: all var(--transition-fast);
    margin-bottom: 0.25rem;
}

.dropdown-item:hover,
.dropdown-item.active {
    background: var(--color-light-gray);
    color: var(--color-primary);
    padding-left: 1rem;
}

.sort-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-primary);
    border-radius: 0.5rem;
    background: var(--color-white);
    color: var(--color-primary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.sort-select:focus {
    outline: none;
    border-color: var(--color-secondary);
    box-shadow: 0 0 0 3px rgba(196, 154, 82, 0.2);
}

/* Results Info */
.results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--color-light-gray);
    flex-wrap: wrap;
    gap: 1rem;
}

.results-count {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.count {
    font-weight: 700;
    color: var(--color-primary);
    font-size: 1.1rem;
}

.active-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    align-items: center;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    background: var(--color-primary);
    color: var(--color-white);
    border-radius: 1rem;
    font-size: 0.85rem;
    font-weight: 500;
}

.filter-tag a {
    color: var(--color-white);
    text-decoration: none;
    font-weight: 700;
    margin-left: 0.25rem;
    transition: color var(--transition-fast);
}

.filter-tag a:hover {
    color: var(--color-secondary);
}

.clear-filters {
    color: var(--color-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    transition: color var(--transition-fast);
}

.clear-filters:hover {
    color: var(--color-primary);
}

.view-toggle {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    width: 40px;
    height: 40px;
    border: 1px solid var(--color-primary);
    background: var(--color-white);
    color: var(--color-primary);
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover,
.view-btn.active {
    background: var(--color-primary);
    color: var(--color-white);
}

/* Products Container */
.products-container {
    min-height: 400px;
}

.products-grid {
    margin-bottom: 3rem;
}

.products-grid.list-view {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.products-grid.list-view .product-card {
    display: grid;
    grid-template-columns: 200px 1fr auto;
    gap: 1.5rem;
    align-items: center;
    padding: 1.5rem;
    background: var(--color-white);
    border-radius: 1rem;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-medium);
}

.products-grid.list-view .product-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.products-grid.list-view .product-image-container {
    height: 150px;
    width: 200px;
    flex-shrink: 0;
}

.products-grid.list-view .product-info {
    padding: 0;
}

.products-grid.list-view .product-actions {
    flex-direction: row;
    gap: 0.5rem;
}

/* Product Cards */
.product-card {
    background: var(--color-white);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-medium);
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.product-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
    flex-shrink: 0;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.85);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-medium);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
}

.product-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 2;
}

.product-badge.featured {
    background: var(--gradient-secondary);
    color: var(--color-white);
}

.product-badge.out-of-stock {
    background: #e74c3c;
    color: var(--color-white);
}

.product-info {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-category {
    color: var(--color-secondary);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.product-title {
    margin-bottom: 0.5rem;
    flex-shrink: 0;
}

.product-title a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.product-title a:hover {
    color: var(--color-secondary);
}

.product-type {
    color: var(--color-medium-gray);
    font-style: italic;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.product-details {
    margin-bottom: 1rem;
}

.product-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--color-medium-gray);
}

.product-detail i {
    color: var(--color-secondary);
    width: 16px;
    text-align: center;
}

.product-description {
    color: var(--color-medium-gray);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    flex: 1;
}

.product-pricing {
    margin-bottom: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--color-light-gray);
}

.price-main {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: 0.25rem;
}

.price-bulk {
    font-size: 0.9rem;
    color: var(--color-medium-gray);
}

.product-stock {
    margin-top: auto;
}

.stock-available {
    color: #10b981;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stock-unavailable {
    color: #e74c3c;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* No Products */
.no-products {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    text-align: center;
}

.no-products-content {
    max-width: 500px;
    padding: 3rem 2rem;
}

.no-products-content i {
    font-size: 4rem;
    color: var(--color-secondary);
    margin-bottom: 1.5rem;
}

.no-products-content h3 {
    color: var(--color-primary);
    margin-bottom: 1rem;
}

.no-products-content p {
    color: var(--color-medium-gray);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.no-products-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* CTA Section */
.cta-section {
    padding: 4rem 0;
}

.cta-content {
    max-width: 700px;
    margin: 0 auto;
}

.cta-content h2 {
    color: var(--color-white);
    margin-bottom: 1rem;
}

.cta-content .text-large {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

.cta-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .advanced-filters {
        justify-content: center;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .page-hero {
        height: 50vh;
        min-height: 300px;
    }

    .filters-wrapper {
        gap: 1.5rem;
    }

    .filter-buttons {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .advanced-filters {
        flex-direction: column;
        gap: 1rem;
    }

    .results-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .products-grid.list-view .product-card {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .products-grid.list-view .product-image-container {
        width: 100%;
        height: 200px;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }

    .cta-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 1rem;
    }

    .filters-section {
        padding: 1.5rem 0;
    }

    .search-input,
    .search-btn {
        padding: 0.75rem 1rem;
    }

    .filter-buttons {
        justify-content: center;
    }

    .filter-btn {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }

    .dropdown-menu {
        min-width: 200px;
        left: 50%;
        transform: translateX(-50%) translateY(-10px);
    }

    .filter-dropdown.active .dropdown-menu {
        transform: translateX(-50%) translateY(0);
    }

    .product-actions {
        gap: 0.5rem;
    }

    .product-actions .btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }

    .no-products-actions {
        flex-direction: column;
        align-items: center;
    }

    .no-products-actions .btn {
        width: 100%;
        max-width: 250px;
    }
}
